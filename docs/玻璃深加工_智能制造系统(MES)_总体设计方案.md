# 玻璃深加工_智能制造系统(MES)_总体设计方案

**文档版本**: 2.2
**创建日期**: 2025-0-15
**作者**: Gemini
**更新日志**: 
- 1.1 - 深化数据模型与车间执行流程。
- 2.0 - 采用"后台智能化，前台简单化"混合方案，集成IoT能力。
- 2.1 - 引入分段式制造与WIP管理模型，增加摆炉优化等深度行业场景。
- 2.2 - 新增智能异常处理与闭环管理章节，完善系统鲁棒性设计。

---

## 1. 概述

本文档旨在详细阐述 **智能排产规划工作台 (Intelligent Production Scheduling Workbench)** 的设计与实现方案。该模块是 MTO-MES 系统的核心决策中枢，旨在解决玻璃深加工行业中 **"交期承诺"** 与 **"成本控制（材料利用率）"** 之间的核心矛盾。

本方案采用 **"后台智能化，前台简单化"** 的设计理念，通过IoT智能设备承担复杂的数据采集和校验任务，为用户提供极简的操作界面。同时，系统在后台构建了一个完整的 **车间数字孪生模型**，能够管理包括 **分段式工艺路径**、**在制品 (WIP) 缓冲**、以及 **瓶颈工位的二次优化（如摆炉优化）** 在内的复杂生产场景。

### 1.1 实际约束条件分析

基于对实施环境的深入调研，本方案充分考虑以下现实约束：
- **用户能力约束**：计划员技术素养参差不齐，需要极简化操作界面
- **车间环境约束**：管理水平一般，人工配合度有限
- **数据基础约束**：缺乏历史数据，需要系统上线后动态演进
- **投入产出约束**：需要快速见效，避免过度复杂的设计

### 1.2 IoT赋能的技术突破

通过集成lens等智能采集设备，本方案实现了关键技术突破：
- **自动数据采集**：无需人工扫码，IoT设备自动识别和记录
- **智能防错校验**：设备自动校验套装ID匹配性，杜绝人为错误
- **实时状态追踪**：全程自动记录玻璃在各工位的状态变化

### 1.3 原型阶段核心目标与量化指标

**技术指标**：
- IoT设备识别准确率：≥99.5%（保证数据采集质量）
- 系统响应时间：≤3秒（保证用户体验）
- 数据同步延迟：≤1秒（保证实时性）

**用户体验指标**：
- 界面操作步骤：≤3步完成排产（极简化操作）
- 用户培训时间：≤30分钟（快速上手）
- 操作错误率：≤2%（降低人为失误）

**业务价值指标**：
- 智能建议采纳率：≥60%（验证算法有效性）
- 材料利用率提升：≥3%（IoT精确追踪支撑）
- **钢化炉满载率提升**：≥5%（通过摆炉优化）
- 排产决策时间：从2小时缩短到30分钟（效率提升）
- 配片错误率：≤0.1%（IoT自动校验保证）

**原型范围限定**：
- **产品类型**：覆盖单片玻璃及中空玻璃（如 `5mm白玻+12A+5mmLowE`）
- **工艺路线**：覆盖分段式的切割→磨边→清洗→（钢化）→合片→包装，并管理工段间的 **WIP缓冲**。
- **IoT设备**：集成lens等智能采集设备，支持自动识别、校验和 **WIP绑定**。
- **异常处理**：处理订单变更、材料短缺、设备故障、玻璃破损等场景。

## 2. 设计思想（基于现实约束的重新设计）

### 2.1. 核心理念："后台智能化，前台简单化"

基于用户素养参差不齐、车间管理水平一般、缺乏历史数据等现实约束，系统采用分层设计理念：

- **前台极简化**：为用户提供最简单直观的操作界面，隐藏技术复杂性
- **后台智能化**：保持完整的数据模型和业务逻辑，确保技术完整性  
- **IoT承担复杂性**：通过lens等智能设备自动处理数据采集、校验、追溯等复杂任务

系统的核心定位是 **增强（Augment）** 而非替代计划员，但通过极简化的界面大幅降低使用门槛。

### 2.2. 用户体验优先的价值导向

考虑到用户素养参差不齐的现实约束，本方案重新定义价值导向：

- **简化决策流程**：将复杂的"交期优先"与"成本优先"选择简化为智能推荐+一键确认
- **渐进式能力释放**：根据用户熟练度逐步开放高级功能
- **容错性设计**：通过IoT设备自动校验，最大化降低人为错误
- **快速见效**：优先实现核心价值（材料利用率提升），避免过度复杂的功能

### 2.3. 分层信息展示策略

```
第一层（默认视图）：订单 + 规格 + 数量 + 交期
第二层（点击展开）：材料利用率 + 预计完工时间  
第三层（专家模式）：详细的组件信息和ID体系
```

用户可以根据自身能力选择合适的信息层级，避免认知过载。

### 2.4. IoT赋能的数据闭环

通过IoT智能设备实现从"规划"到"执行"的无缝衔接：
*   **BOM自动分解**：后台基于BOM自动分解复合产品，前台按成品展示
*   **智能批次管理**：系统自动创建 **"生产批次 (Production Batch)"**，用户无感知
*   **IoT自动赋码**：lens等设备自动为每片玻璃分配 **"唯一小片ID"** 和 **"套装ID"**
*   **智能防错校验**：IoT设备自动校验组件匹配性，无需人工干预

## 3. 业务流程（升级版）

### 3.1 用户操作流程（极简化）

1.  **进入工作台**：计划员看到简洁的订单列表界面
2.  **一键智能排产**：
    *   点击"智能排产"按钮
    *   系统后台自动进行BOM分解、组件任务创建、批次生成
    *   前台显示优化后的生产计划建议
3.  **简单调整**：通过拖拽调整生产顺序（可选）
4.  **确认执行**：点击"确认执行"，系统推送到IoT设备

### 3.2 系统后台处理流程（用户无感知）

1.  **数据预处理**：
    *   自动加载"已发布"的工单
    *   对中空玻璃等复合产品进行BOM分解
    *   创建组件生产任务和生产批次
2.  **智能优化**：
    *   识别可合并的同类组件任务
    *   计算最优的材料利用率方案
    *   生成排产建议
3.  **IoT设备协调**：
    *   为每个生产批次分配唯一小片ID和套装ID
    *   推送切割清单到lens等智能设备
    *   设置自动校验规则

### 3.3 车间执行流程（分段式IoT自动化）

原有的线性流程已无法描述真实的、带缓冲的生产过程。现升级为更贴近现实的分段式流程：

1.  **冷工段（切割、磨边、清洗）**
    *   **自动赋码**：切割机配合lens设备，为每片玻璃自动分配并记录 `唯一小片ID` 和 `套装ID`。
    *   **下线落架**：冷工段连线生产结束后，在下线口，工人将玻璃放置在中转架上。
    *   **WIP绑定**：IoT设备（或工人的PDA）自动/手动扫描玻璃ID和中转架ID（如 `RACK-007`），完成 **“玻璃”与“货架”的绑定**。系统此时精确知道，`RACK-007` 上装载了哪些玻璃。
    *   **状态更新**：该批玻璃状态更新为“冷工段完工，待入钢化缓冲”，并被运送至指定缓冲区域。

2.  **钢化工段（瓶颈与二次优化）**
    *   **触发摆炉优化**：当钢化炉即将空闲时，系统或班组长触发 **“摆炉优化”** 任务。
    *   **智能计算**：系统从“钢化缓冲池”中，根据优先级、交期、玻璃厚度、尺寸等约束，自动选取最优的一炉玻璃组合，并生成 **“摆炉图”** 和唯一的 **“钢化炉次号 (TemperingBatchID)”**。
    *   **按图索骥**：上片工位的终端上会显示清晰的摆炉图，并明确指示：“请从 `RACK-007` 取 `3` 片，从 `RACK-012` 取 `8` 片...”。
    *   **上炉跟踪**：工人按图上片，IoT设备记录每一片玻璃已进入钢化炉，并与 `TemperingBatchID` 关联。
    *   **下炉落架**：钢化完成后，玻璃被放置在新的中转架上，并再次进行绑定，状态更新为“钢化完工，待入合片缓冲”。

3.  **合片工段（精准校验）**
    *   **按需送料**：系统根据合片工位的生产节拍，通知AGV或工人将装载着匹配组件的中转架（例如，装外片的 `RACK-015` 和装内片的 `RACK-021`）运送至工位旁。
    *   **自动校验**：工人取片上料，IoT设备自动进行 `套装ID` 的强制校验。对于弯钢等特殊工艺，系统还会 **双重校验 `钢化炉次号`** 是否一致。
    *   **异常报警**：不匹配时设备自动报警并暂停操作。

## 4. 技术架构设计

### 4.1 分层架构

```
用户界面层（极简化）
    ↓
业务逻辑层（复杂处理）
    ↓  
IoT设备层（自动执行）
    ↓
数据存储层（完整追溯）
```

### 4.2 关键数据模型（扩展）

**新增核心数据模型：**
```javascript
// 中转架 (WIP Carrier / Rack)
{
  rackId: "RACK-007",
  location: "Tempering_Buffer_Zone_A", // 物理位置/逻辑区域
  content: ["BATCH-001-P01", "BATCH-001-P03", ...], // 装载的玻璃Piece ID列表
  status: "WAITING_FOR_TEMPERING", // 等待钢化
  capacity: 50,
  currentLoad: 35
}

// 钢化炉次 (Tempering Batch)
{
  temperingBatchId: "TB-20250816-001",
  furnaceId: "Furnace-1",
  startTime: "2025-08-16T14:00:00Z",
  content: ["BATCH-002-P05", "BATCH-003-P11", ...], // 本炉次包含的玻璃Piece ID
  loadingMap: { ... } // 摆炉优化生成的布局图数据
}
```

**原有数据模型扩展：**
```javascript
// 在完整的后台数据模型中，为每个Piece增加位置和状态信息
{
  pieceId: "BATCH-001-P01",
  kitId: "SO-001-FPU-001",
  // ... 其他信息
  currentStatus: "ON_RACK",
  currentLocation: {
    type: "RACK",
    id: "RACK-007"
  },
  temperingBatchId: null // 钢化后会被赋值
}
```

### 4.3 IoT集成接口

```javascript
// 推送到IoT设备
iotService.pushCuttingList(batchId, cuttingList)

// 接收设备状态
iotService.onStatusUpdate((pieceId, status, timestamp) => {
  // 更新追溯记录
})

// 校验匹配性
iotService.validateKitMatch(kitId1, kitId2) => boolean
```

## 5. 功能模块深度设计

为了实现“后台智能化，前台简单化”的目标，我们将功能模块划分为 **“用户交互层”** 和 **“后台服务层”** 两大部分。

### 5.1 用户交互层模块 (User-Facing Modules)

用户交互层遵循极简和角色聚焦原则，为不同用户提供其完成核心任务所需的最少必要界面。

#### 5.1.1 计划员工作台模块 (Planner's Workbench)

*   **目标**：为生产计划员提供一个高效、直观的宏观计划制定工具。
*   **核心组件**:
    *   **待办订单池 (Order Pool)**:
        *   **功能**: 以卡片形式展示所有状态为“已审核，待排产”的客户订单。
        *   **UI设计**: 强调状态可视化（紧急/正常/宽松）、核心信息（客户、交期、规格、数量）。提供一个全局的“全选加入”和单个订单的“加入排产”按钮。提供基础的搜索和按交期/客户过滤功能。
    *   **排产队列 (Scheduling Queue)**:
        *   **功能**: 一个临时的暂存区，用于存放计划员勾选的、准备进行一次“智能排产”计算的订单集合。
        *   **UI设计**: 以小标签或头像形式展示已选中的订单号，并实时显示订单总数和玻璃总片数。
    *   **主控操作面板 (Master Control Panel)**:
        *   **功能**: 这是计划的核心触发器。
        *   **UI设计**: 包含一个非常醒目的 **“一键智能排产”** 按钮。点击后，系统接管所有复杂计算。按钮下方会有一个进度条和状态文本（例如：“步骤1/3: 正在进行BOM分解...”、“步骤2/3: 正在计算切割优化方案...”）。
    *   **生产计划看板 (Production Kanban)**:
        *   **功能**: 可视化展示未来一段时间内（例如7天），关键瓶颈资源（如钢化炉、夹胶线）的产能负载和已安排的生产任务。
        *   **UI设计**: Y轴为瓶颈资源，X轴为时间（可切换日/周视图）。任务块以“生产批次”为单位显示，其长度代表预计占用时间。提供有限的拖拽微调功能，并实时反馈对交期的影响。

#### 5.1.2 车间执行终端模块 (Shop Floor Terminal)

*   **目标**：为一线工人和班组长提供指导操作、反馈状态和上报异常的极简终端界面。
*   **核心组件 (根据工位不同而变化)**:
    *   **摆炉指导终端 (Tempering Loading Guide)**:
        *   **功能**: 在钢化炉上片工位，清晰地向工人展示当前“钢化炉次”需要从哪些中转架上取哪些玻璃，以及如何在炉内摆放。
        *   **UI设计**: 左侧为列表，指示“从RACK-007取3片”，右侧为可视化的摆炉图。工人每取一片，IoT设备自动识别，列表对应项自动打勾，提供清晰的进度反馈。
    *   **智能校验终端 (Smart Validation Terminal)**:
        *   **功能**: 在合片等需要精确配对的工位，提供自动化的防错校验。
        *   **UI设计**: 如9.2节所述，采用全屏颜色、巨大图标和声音进行极限的视觉反馈，与设备PLC联动实现物理防错。
    *   **WIP管理终端 (WIP Management Terminal)**:
        *   **功能**: 在各工段的下线口，用于执行“玻璃”与“中转架”的绑定（落架）和解绑（上料）。
        *   **UI设计**: 界面极其简单，只有“绑定到货架”和“从货架取料”两个大按钮。流程由IoT设备或PDA扫码驱动，界面仅用于显示结果（“绑定成功：35片玻璃已存入RACK-007”）和处理异常。

#### 5.1.3 移动监控驾驶舱 (Mobile Dashboard)

*   **目标**：为管理层（车间主任、生产经理）提供在移动端随时随地掌握生产全局的能力。
*   **核心组件**:
    *   **全局KPI概览 (Overall KPI View)**:
        *   **功能**: 展示订单完成率、设备综合效率(OEE)、在制品(WIP)总库存、异常事件数等核心指标。
    *   **瓶颈资源监控 (Bottleneck Monitor)**:
        *   **功能**: 实时显示瓶颈设备（钢化炉）的当前状态、负载、等待队列和预计产出。
    *   **WIP缓冲池看板 (WIP Buffer Kanban)**:
        *   **功能**: 可视化展示各工段之间缓冲区的实时库存状态（料架数量、玻璃片数），并用颜色预警（绿色-健康，黄色-偏低，红色-即将断料）。
    *   **异常处理中心 (Exception Center)**:
        *   **功能**: 集中推送和管理所有生产异常事件（破损、设备故障、缺料），并允许管理者在线指派和跟踪处理进度。

### 5.2 后台服务层模块 (Backend Service Modules)

后台服务层是实现“智能化”的核心，用户对此无感知，但它们是系统的大脑和中枢神经。

#### 5.2.1 订单与BOM解析服务 (Order & BOM Parsing Service)

*   **职责**:
    *   从ERP或订单系统接收客户订单。
    *   根据产品库中的BOM定义，自动将复合产品订单（如中空玻璃）“分解”为一系列可独立生产的“组件任务”。
    *   为每个任务附加所有必要的工艺参数和生产要求。

#### 5.2.2 智能排产引擎 (Intelligent Scheduling Engine)

*   **职责**:
    *   接收计划员选择的订单（或组件任务）队列。
    *   **宏观优化 (Macro-Optimization)**: 核心算法，综合考虑交期、客户优先级、物料库存、设备产能等约束，以“综合成本最低”或“客户满意度最高”为目标，生成初始的“生产批次”建议和时间规划。
    *   **二次优化 (Micro-Optimization)**:
        *   **切割优化 (Nesting Service)**: 与专业排版软件对接，计算每个“生产批次”的最佳切割方案，目标是 **材料利用率最大化**。
        *   **摆炉优化 (Tempering Loading Service)**: 在钢化前，从WIP缓冲池中动态选择玻璃，计算单炉 **面积利用率最大化** 的摆放方案。

#### 5.2.3 车间数字孪生服务 (Digital Twin Service)

*   **职责**: 这是系统的核心状态管理器，负责构建和维护车间的实时数字镜像。
    *   **身份管理 (Identity Management)**: 创建并管理每一个 `Production Batch`, `Piece ID`, `Kit ID`, `Tempering Batch ID` 的生命周期。
    *   **位置跟踪 (Location Tracking)**: 实时记录每一片玻璃 (`Piece ID`) 和每一个中转架 (`Rack ID`) 的物理位置和逻辑状态（例如：`RACK-007` 位于 `钢化前缓冲A区`，状态为 `待钢化`）。
    *   **状态机引擎 (State Machine Engine)**: 管理每个生产对象的标准状态流转（例如：`待生产` -> `切割中` -> `在货架上` -> `钢化中` -> `已完工`），并处理异常状态。

#### 5.2.4 IoT集成与协调服务 (IoT Integration & Orchestration Service)

*   **职责**: 作为MES与物理世界之间的桥梁。
    *   **设备适配**: 提供与不同IoT设备（lens、PLC、RFID读取器、智能料架）通信的标准化接口。
    *   **任务分发**: 将后台生成的指令（如切割清单、摆炉图）准确下发到对应的车间终端或设备。
    *   **数据采集与解析**: 接收来自IoT设备的原始数据（如图像、ID、传感器读数），解析并转化为MES可理解的结构化事件（如“`Piece-001` 已进入钢化炉”）。

## 6. 实施方案（分阶段渐进）

### 阶段一：MVP核心功能（2个月）

**目标**：验证核心价值假设，快速上线

**功能范围**：
- 简化的订单展示界面
- 一键智能排产功能
- 基础的生产计划看板
- 简单的IoT设备集成

**技术实现**：
- 前端：Vue3 + 极简UI组件
- 后端：基础的BOM分解和批次管理
- IoT：lens设备基础集成
- 数据：简化的Mock数据

**验收标准**：
- 用户能在30分钟内学会操作
- 材料利用率提升≥2%
- 系统响应时间≤3秒

### 阶段二：智能优化（3个月）

**目标**：基于阶段一数据，优化算法和用户体验

**新增功能**：
- 基于历史数据的智能建议优化
- 更精确的交期预测
- **新增：WIP缓冲池管理与可视化**
- **新增：钢化摆炉优化模块**
- 异常处理和报警机制
- 高级用户的专家模式

**技术升级**：
- 机器学习算法集成
- 更完善的IoT设备协调
- 实时数据分析和反馈

### 阶段三：全面集成（6个月）

**目标**：完整的端到端解决方案

**完善功能**：
- 完整的异常处理机制
- 多工厂、多产线支持
- 高级分析和报表功能
- 移动端支持

## 7. 风险控制与应对策略

### 7.1 技术风险

**IoT设备可靠性风险**：
- 风险：lens设备识别准确率不达标
- 应对：建立设备降级方案，支持人工辅助模式

**数据同步风险**：
- 风险：多设备间数据不一致
- 应对：建立数据校验和修复机制

### 7.2 用户接受度风险

**学习曲线风险**：
- 风险：用户难以适应新系统
- 应对：提供详细培训和分步引导

**信任度风险**：
- 风险：用户不信任"黑盒"系统
- 应对：提供透明的决策解释和手动干预选项

### 7.3 业务风险

**投资回报风险**：
- 风险：系统复杂度超出预期收益
- 应对：严格控制功能范围，优先核心价值

**实施周期风险**：
- 风险：开发周期过长，错失市场机会
- 应对：采用MVP方式，快速迭代

## 8. 成功评估标准

### 8.1 短期指标（3个月内）

- 系统使用率：≥80%
- 用户培训完成率：≥95%
- 操作错误率：≤2%
- 材料利用率提升：≥2%

### 8.2 中期指标（6个月内）

- 智能建议采纳率：≥60%
- 排产效率提升：≥50%
- IoT设备识别准确率：≥99%
- 用户满意度：≥4.0/5.0

### 8.3 长期指标（12个月内）

- 材料利用率提升：≥3%
- 配片错误率：≤0.1%
- **新增：钢化炉满载率提升**：≥5%
- 系统ROI：≥200%
- 可扩展性验证：支持多产线部署

---

**总结**：本修订方案充分考虑了实际约束条件，采用"后台智能化，前台简单化"的设计理念，通过IoT设备承担复杂性，为用户提供极简的操作体验。方案既保持了技术完整性，又大幅降低了实施难度和用户门槛，是一个更加务实和可行的解决方案。

---

## 9. 界面原型深度设计 (Usability-Focused)

#### 9.1 设计哲学：面向最普通的用户，提供最可靠的工具

考虑到车间环境的复杂性和用户背景的多样性，我们的UI/UX设计必须遵循以下原则：

1.  **零学习成本**：界面元素应符合用户在消费级应用（如微信、抖音）中已形成的直觉，避免使用专业术语和复杂图表。
2.  **视觉引导优先**：大量使用颜色、图标、大字体和动画反馈，让用户“看”一眼就知道状态，而不是“读”一段文字。
3.  **防错大于纠错**：在用户可能犯错的地方，通过禁用按钮、强制校验、醒目提示等方式提前阻止，而不是等错误发生后再让用户处理。
4.  **任务聚焦**：每个界面只服务于一个核心任务，避免信息过载。
5.  **移动优先与设备适配**：界面设计需同时考虑PC端的大屏看板和车间内手持终端（PDA、平板）或工位触摸屏的小屏幕操作。

#### 9.2 核心界面原型设计

##### **界面一：生产计划员工作台 (PC端)**

这是计划员的核心操作界面，目标是 **“三步完成排产”**。

**原型图景 (Wireframe Concept):**

```
+---------------------------------------------------------------------------------------------------+
| [GlassERP Logo]  智能排产工作台                                        [用户头像] [帮助] [退出]   |
+---------------------------------------------------------------------------------------------------+
|                                                                                                   |
|  [ 待处理订单 (15) ]                                                                              |
|                                                                                                   |
|  +---------------------------------+   +---------------------------------+   +-----------------+   |
|  | SO-001 (紧急) 🚨             |   | SO-002 (正常)                   |   | SO-003 (宽松)   |   |
|  | 客户: 远大幕墙                  |   | 客户: 城市之窗                  |   | ...             |   |
|  | 产品: 5+12A+5 Low-E (80片)      |   | 产品: 8mm钢化 (120片)           |   |                 |   |
|  | 交期: 明天 17:00 前             |   | 交期: 2025-08-18                |   |                 |   |
|  | [✅ 加入排产]                   |   | [✅ 加入排产]                   |   |                 |   |
|  +---------------------------------+   +---------------------------------+   +-----------------+   |
|                                                                                                   |
|  +-----------------------------------------------------------------------------------------------+   |
|  | [ 已选排产队列 (3个订单, 共350片玻璃) ]                                                       |   |
|  |                                                                                               |   |
|  |  SO-001 | SO-002 | SO-004                                                                     |   |
|  |                                                                                               |   |
|  |                                [ ✨ 一键智能排产 ✨ ]  (系统将优化材料并安排计划)               |   |
|  +-----------------------------------------------------------------------------------------------+   |
|                                                                                                   |
|  [ 生产计划看板 (瓶颈资源: 钢化炉) ]                                                              |   |
|  +-----------------------------------------------------------------------------------------------+   |
|  | 时间轴 -> | 今天 08-16 | 明天 08-17 | 后天 08-18 | ...                                         |   |
|  |-----------|------------|------------|------------|---------------------------------------------|   |
|  | 钢化炉 #1 | [BATCH-001] [BATCH-002]  | [BATCH-003]  | (空闲)                                      |   |
|  |           | (8:00-11:30) (13:00-18:00) | (8:00-15:00) |                                             |   |
|  +-----------------------------------------------------------------------------------------------+   |
|                                                                                                   |
+---------------------------------------------------------------------------------------------------+
```

**设计细节与易用性考虑：**

1.  **订单卡片化**：
    *   **状态可视化**：用 Emoji (🚨) 和颜色标签（红色边框）直观表示“紧急”状态，比文字更醒目。
    *   **信息精炼**：只展示计划员最关心的4个核心信息：客户、产品、数量、交期。
    *   **单一操作**：每个卡片只有一个明确的行动点 `[✅ 加入排产]`，避免选择困难。点击后，卡片会以流畅的动画飞入下方的“排产队列”区域，提供即时、愉悦的视觉反馈。

2.  **核心操作区**：
    *   **“一键智能排产”按钮**：这是整个界面的核心。设计上必须 **巨大、醒目、有吸引力**，使用发光或呼吸动画效果（✨），并配有通俗易懂的说明文字。这是引导用户完成核心任务的“重力点”。
    *   **用户无感知后台处理**：用户点击后，按钮变为“正在计算中...”，并显示进度条。用户完全不需要理解BOM分解、批次创建等复杂后台过程。

3.  **生产计划看板**：
    *   **简化展示**：看板上的任务块只显示最重要的信息——批次号和预计占用时间。
    *   **交互式详情**：鼠标悬浮在 `[BATCH-001]` 上时，才会浮出一个小窗，显示该批次包含了哪些订单（SO-001, SO-005...），以及预估的材料利用率。这符合“渐进式信息披露”原则。
    *   **拖拽调整**：允许计划员在看板上拖拽已生成的批次块来微调顺序，提供有限但必要的灵活性。拖拽时，系统会实时提示“将影响XXX订单延期风险”，进行即时反馈。

##### **界面二：合片工位校验终端 (触摸屏/平板)**

这是车间一线工人使用的界面，目标是 **“傻瓜式操作，绝对防错”**。

**原型图景 (Wireframe Concept):**

```
+------------------------------------------+
| [GlassERP Logo]  中空合片校验工位 #3     |
+------------------------------------------+
|                                          |
|      请放置第一片玻璃 (外片)             |
|                                          |
|      [  (一个巨大的玻璃图标)  ]          |
|      [ (摄像头/传感器区域) ]             |
|                                          |
|      系统将自动识别...                   |
|                                          |
+------------------------------------------+

--- 当第一片玻璃被IoT设备识别后 ---

+------------------------------------------+
| [GlassERP Logo]  中空合片校验工位 #3     |
+------------------------------------------+
|                                          |
|      ✅ 外片识别成功！                   |
|      套装号: SO-001-FPU-007              |
|                                          |
|      请放置匹配的第二片玻璃 (内片)       |
|                                          |
|      [  (一个巨大的玻璃图标)  ]          |
|      [ (摄像头/传感器区域) ]             |
|                                          |
+------------------------------------------+

--- 当第二片玻璃被识别，且匹配成功时 ---

+------------------------------------------+
|                                          |
|                                          |
|          ✅ 匹配成功！ ✅                |
|          (全屏绿色背景)                  |
|          (巨大的对勾图标)                |
|                                          |
|          设备已解锁，请开始合片          |
|                                          |
|                                          |
+------------------------------------------+

--- 当第二片玻璃被识别，但匹配失败时 ---

+------------------------------------------+
|                                          |
|                                          |
|          ❌ 严重错误：玻璃不匹配！❌      |
|          (全屏红色背景 + 闪烁)           |
|          (巨大的叉号图标 + 警报声)       |
|                                          |
|      这片玻璃属于套装: SO-001-FPU-015    |
|      请立即通知班组长！                  |
|      (设备已锁定)                        |
|                                          |
+------------------------------------------+
```

**设计细节与易用性考虑：**

1.  **无操作界面**：工人几乎不需要进行任何点击操作。整个流程由IoT设备（lens摄像头）自动驱动，界面只是一个 **状态显示器**。
2.  **极限的视觉反馈**：
    *   **全屏颜色**：用绿色代表“通过”，红色代表“错误”。即使工人在几米外，眼角余光也能感知到状态。
    *   **巨大图标和文字**：字体极大，确保在任何光线和距离下都清晰可见。
    *   **声音与动画**：成功时有“叮”的悦耳提示音，失败时有急促的警报声和闪烁效果，调动多感官进行提醒。
3.  **明确的指令**：指令语言极其简单直白，如“请放置第一片玻璃”、“请开始合片”、“请立即通知班组长”。
4.  **物理防错联动**：最重要的设计是，在校验失败时，界面不仅是提示，而是通过与设备PLC的联动，**物理上锁定设备**，从根本上杜绝了工人“无视警告，继续操作”的可能性。

##### **界面三：班组长/车间主任移动看板 (升级版)**

此界面升级为车间的 **“数字孪生驾驶舱”**，不仅看设备，更要看缓冲池。

**原型图景 (Wireframe Concept):**

```
+------------------------------------------+
| [GlassERP] 生产实时看板                  |
+------------------------------------------+
| [概览] [切割] [钢化] [合片] [异常] (5) 🚨 |
+------------------------------------------+
| == 钢化炉 #1 (瓶颈) ==                    |
| 状态: 🔥 生产中 (800°C)                  |
| 炉次: TB-001 (85% 满载率)                |
| 进度: [████████░░░░░░] 60% (还剩45分钟) |
|------------------------------------------|
| == 钢化前缓冲池 (WIP) ==                 |
| 总容量: 200片 / 5个料架                  |
| 当前库存: 158片 / 4个料架 (79%)          |
| > RACK-007 (35片, 待钢化) [查看详情]     |
| > RACK-012 (48片, 待钢化) [查看详情]     |
|------------------------------------------|
| == 合片工位 #3 ==                        |
| 状态: 🟢 正常                           |
| 效率: 25套/小时 (低于目标 10%)           |
| 待料情况: 外片(2架), 内片(1架) - ⚠️ 内片即将用尽 |
|------------------------------------------|
| == 异常报警 (5) 🚨 ==                    |
| > 11:05 合片#3 配对错误 (已处理)         |
| > 10:40 切割机#1 缺料: 5mm Low-E (待处理)|
|   [点击查看详情并处理]                   |
+------------------------------------------+
```

**升级设计细节：**

1.  **增加WIP缓冲池监控**：这是最重要的升级。管理者可以实时看到各工段之间缓冲区的库存量，单位可以是“片”和“料架”。这对于预判瓶颈、防止断料至关重要。
2.  **状态信息更丰富**：钢化炉状态增加了温度、炉次号、满载率等关键工艺参数。合片工位增加了“待料情况”预警，如 `⚠️ 内片即将用尽`，将隐患显性化。
3.  **可钻取的数据**：点击 `[查看详情]`，可以弹窗显示该料架上所有玻璃的订单归属、规格等详细信息，实现了从宏观到微观的全面追溯。

---

## 10. 分段式智能制造模型深度设计

### 10.1 数字与物理的孪生：在制品(WIP)与中转架管理

玻璃深加工的本质是分段式的，各工段之间通过中转架（料架）进行缓冲和解耦。本系统通过将 **“中转架”** 数字化，构建了车间物流的数字孪生。

- **核心实体**：`中转架 (WIP Rack)` 在MES中被视为一个核心管理对象，拥有唯一的ID、位置、状态和内容清单。
- **数据绑定**：通过IoT设备（固定式扫描器或移动终端），实现每一片玻璃的 `Piece ID` 与其所在的 `Rack ID` 的动态绑定和解绑。
- **两种模式**：
    - **传统模式**：工人在落架时，用PDA依次扫描架子二维码和玻璃二维码完成绑定。
    - **智能料架模式 (IoT)**：料架本身集成RFID/BLE等技术，自动感知架上玻璃的进出，并实时上报MES，实现无人化WIP管理。本方案以后者为最终目标。

### 10.2 瓶颈工段的深度优化：钢化摆炉

“一键智能排产”解决的是“哪些订单应该在什么时间窗口生产”的宏观问题。而 **“摆炉优化”** 解决的是“在当前时间点，如何将待钢化的玻璃最高效地组合进一炉”的微观执行问题。

- **定位**：一个嵌入在钢化工段前的 **二次动态优化引擎**。
- **输入**：钢化前缓冲池中所有状态为“待钢化”的玻璃列表。
- **优化目标**：在满足交期和工艺约束（如厚度、镀膜类型）的前提下，实现单炉 **面积利用率最大化**。
- **输出**：一个包含最佳玻璃组合的 **“钢化炉次 (Tempering Batch)”**，附带一张可视化的 **“摆炉指导图”**，并生成唯一的 `TemperingBatchID`。
- **价值**：不仅提升了设备产能，其输出的 `TemperingBatchID` 更是弯钢弧度匹配、质量问题追溯到炉的重要依据。

---
## 11. 新增：智能异常处理与闭环管理

本章节详细阐述系统如何应对车间常见异常，实现快速响应、智能决策和流程闭环，确保生产的连续性和鲁棒性。

### 11.1 设计原则

- **主动预警**：通过IoT数据和算法，提前预判风险（如物料即将耗尽），将异常处理从事后补救变为事前预防。
- **简化上报**：为一线工人提供最简单的异常上报方式（如工位终端一键上报、IoT自动检测）。
- **智能决策支持**：系统自动分析异常影响，并为管理者提供量化的解决方案选项。
- **流程闭环**：确保每一个异常事件，从发生、响应、处理到验证，都有一个完整的数字化记录和状态跟踪。

### 11.2 核心异常场景处理流程

#### **场景一：玻璃破损 / 质量不合格**

这是最高频的异常，处理效率直接影响交付。

1.  **检测与上报**：
    *   **IoT自动检测**：在关键质检工位，AI视觉设备（如lens）自动识别出瑕疵或破损，并根据玻璃ID自动上报异常。
    - **人工一键上报**：任何工位的工人发现破损，都可以在最近的终端上，点击“破损上报”按钮。如果当前工位有IoT设备，系统会自动识别当前玻璃ID；否则，工人可用PDA扫描玻璃二维码完成上报。

2.  **系统后台响应（用户无感知）**：
    *   **状态标记**：系统立即将该 `Piece ID` 的状态标记为“已报废”，并记录原因、工位、时间。
    *   **创建补片任务**：系统自动复制该玻璃的所有信息（尺寸、工艺、归属的`Kit ID`等），生成一个全新的 **“补片任务 (Remake Task)”**。
    *   **优先级提升**：该“补片任务”被自动赋予最高优先级（`Priority: URGENT`）。
    *   **影响分析**：系统自动分析该补片对成品交期的影响，如果可能导致延期，则自动向计划员和跟单员发送预警。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：在“待处理订单”区域，出现一个醒目的红色卡片，标记为“紧急补片任务”。
    *   **智能插入**：计划员在下一次执行“一键智能排产”时，算法会强制将这个高优先级的补片任务插入到最优的生产批次中（例如，与相同材质和厚度的玻璃合并）。
    *   **班组长移动看板**：“异常”列表中出现该条记录，并实时跟踪补片任务的进度，直至其完成。

#### **场景二：关键设备故障**

1.  **检测与上报**：
    *   **IoT自动检测**：通过连接设备PLC，系统实时监控设备状态。一旦接收到故障代码或心跳停止，立即触发“设备故障”流程。
    *   **人工上报**：班组长在移动看板上，选择对应设备，点击“故障上报”，并可选择故障类型（如“机械故障”、“电气故障”）。

2.  **系统后台响应**：
    *   **冻结计划**：系统立即“冻结”该设备上所有已规划但未开始的“生产批次”。
    *   **全局重计算**：系统以该设备不可用为约束，重新计算所有受影响订单的预计完工时间 (ETA)。
    *   **生成报告**：快速生成一份“故障影响报告”，包含受影响的订单列表、预计延误时间等。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：生产计划看板上，故障设备的时间轴变为灰色不可用状态。所有被冻结的批次变为红色闪烁状态。系统弹出通知：“钢化炉#1发生故障，预计停机2小时。15个订单交期可能受影响。[查看影响报告并重排]”。
    *   **一键重排**：计划员在修复时间预估输入后，可以点击“一键应急重排”，系统会自动寻找替代路径（如使用备用设备）或按最新优先级重新计算一个可行的计划。

#### **场景三：物料短缺**

1.  **检测与预警**：
    *   **实时库存监控**：系统对接WMS或管理原片/辅材库存，根据当前生产计划实时扣减预期用量。
    *   **主动预警**：当系统预测到某规格原片将在未来X小时内（可配置，如4小时）低于安全库存时，自动触发“物料短缺预警”。

2.  **系统后台响应**：
    *   **风险标记**：将所有依赖该物料且计划在预警时间窗口内生产的“生产批次”标记为“物料风险”。
    *   **方案建议**：系统可根据预设规则，自动寻找替代物料（如用更大规格原片替代），并计算其成本影响。

3.  **用户界面呈现与决策**：
    *   **计划员工作台**：看板上，有风险的批次块边缘变为黄色。系统通知：“5mm Low-E原片预计在4小时后用尽，影响3个生产批次。[查看解决方案]”。
    *   **决策支持**：点击通知后，系统弹窗提供选项：
        *   **选项A (物料替代)**：“使用 3660x2440 规格替代，成本增加 5%，不影响交期。”
        *   **选项B (调整计划)**：“暂停这3个批次，优先生产其他批次。预计影响 SO-008 订单延期1天。”
        *   **选项C (手动处理)**：“通知采购紧急补料。”
    计划员可以根据商业利益（如客户重要性、成本容忍度）快速做出决策。

通过以上设计，系统不仅能执行最优计划，更能在一个动态、充满不确定性的真实车间环境中，成为管理者应对异常、降低损失、保障交付的智能副驾。
