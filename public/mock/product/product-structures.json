{"productStructures": [{"id": "fw_std_001", "code": "FW-STD-001", "name": "标准防火窗结构", "description": "符合国标GB16809-2008的A级防火窗，60分钟耐火极限", "productType": "window", "category": "防火窗", "subCategory": "固定式防火窗", "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-15T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001", "rootAssembly": {"id": "asm_fw_main", "assemblyId": "asm_fw_main", "assemblyCode": "FW-MAIN-ASM", "assemblyName": "防火窗主框架构件", "assemblyVersion": 1, "instanceName": "主框架总成", "quantity": 1, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "parameterValues": {"frame_width": 80, "frame_depth": 120, "corner_joint_type": "welded"}, "optional": false, "alternatives": [], "properties": {"material_grade": "Q355B", "surface_treatment": "galvanized", "fire_rating": "A"}, "parameters": [{"id": "param_frame_width", "name": "frame_width", "displayName": "框架宽度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 80, "minValue": 60, "maxValue": 120, "required": true, "helpText": "框架型材的宽度，影响承重能力"}, {"id": "param_frame_depth", "name": "frame_depth", "displayName": "框架深度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 120, "minValue": 80, "maxValue": 160, "required": true, "helpText": "框架型材的深度，影响安装和密封"}], "constraints": [{"id": "const_frame_001", "name": "框架尺寸约束", "expression": "frame_width >= 60 && frame_depth >= 80", "errorMessage": "框架尺寸不符合防火窗标准要求", "severity": "error"}], "componentInstances": [{"id": "comp_fw_vertical_left", "componentId": "comp_fw_vertical", "componentCode": "FW-VERT-001", "componentName": "外框立柱", "instanceName": "左立柱", "quantity": 1, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "parameterValues": {"height": 1800, "profile_section": "80x120x4", "material_grade": "Q355B"}, "optional": false, "alternatives": [], "properties": {"materialCategoryId": "cat_steel_profile", "quantityFormula": "height / 1000", "costFormula": "quantity * material_unit_cost * 1.2"}, "parameters": [{"id": "param_vert_height", "name": "height", "displayName": "立柱高度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 1800, "minValue": 800, "maxValue": 3000, "required": true, "helpText": "立柱的总高度，需考虑安装余量"}]}, {"id": "comp_fw_glass_main", "componentId": "comp_fw_glass", "componentCode": "FW-GLASS-001", "componentName": "防火玻璃面板", "instanceName": "主玻璃面板", "quantity": 1, "position": {"x": 40, "y": 40, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "parameterValues": {"glass_width": 1420, "glass_height": 1720, "glass_thickness": 6, "fire_rating": "60min", "glass_type": "fire_resistant"}, "optional": false, "alternatives": [], "properties": {"materialCategoryId": "cat_fire_glass", "quantityFormula": "(glass_width * glass_height) / 1000000", "costFormula": "quantity * material_unit_cost * (glass_thickness / 6)"}, "parameters": [{"id": "param_glass_width", "name": "glass_width", "displayName": "玻璃宽度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 1420, "minValue": 500, "maxValue": 2900, "required": true, "helpText": "玻璃面板的净宽度，需考虑框架尺寸"}, {"id": "param_fire_rating", "name": "fire_rating", "displayName": "防火等级", "type": "feature", "inputType": "select", "defaultValue": "60min", "required": true, "options": [{"value": "30min", "label": "30分钟", "additionalCost": 0}, {"value": "60min", "label": "60分钟", "additionalCost": 100}, {"value": "90min", "label": "90分钟", "additionalCost": 250}, {"value": "120min", "label": "120分钟", "additionalCost": 400}], "helpText": "防火玻璃的耐火时间等级"}]}]}, "productParameters": [{"id": "prod_param_width", "name": "window_width", "displayName": "窗户宽度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 1500, "minValue": 600, "maxValue": 3000, "required": true, "helpText": "防火窗的净宽度尺寸"}, {"id": "prod_param_opening", "name": "opening_method", "displayName": "开启方式", "type": "feature", "inputType": "select", "defaultValue": "fixed", "required": true, "options": [{"value": "fixed", "label": "固定窗", "additionalCost": 0}, {"value": "casement", "label": "平开窗", "additionalCost": 400}, {"value": "sliding", "label": "推拉窗", "additionalCost": 350}], "helpText": "窗户的开启方式，影响五金配置"}], "productConstraints": [{"id": "const_prod_001", "name": "窗户面积约束", "expression": "window_width * window_height <= 6000000", "errorMessage": "窗户面积不能超过6平方米", "severity": "error"}], "validationRules": [], "configurationOptions": [], "versionHistory": [], "applications": ["商业建筑", "住宅建筑", "工业建筑"], "metadata": {"designStandard": "GB16809-2008", "certificationRequired": true, "testReportRequired": true, "qualityLevel": "A", "manufacturer": "标准玻璃深加工厂"}, "tags": ["防火窗", "标准", "A级", "60分钟", "钢质框架", "防火玻璃"]}, {"id": "fp_premium_001", "code": "FP-PREM-001", "name": "高端防火隔断结构", "description": "不锈钢框架+夹胶防火玻璃，120分钟耐火极限，适用于高端场所", "productType": "partition", "category": "防火隔断", "subCategory": "高端隔断", "version": 1, "status": "active", "createdAt": "2024-01-02T00:00:00Z", "updatedAt": "2024-01-20T00:00:00Z", "createdBy": "engineer_002", "updatedBy": "engineer_002", "rootAssembly": {"id": "asm_fp_main", "assemblyId": "asm_fp_main", "assemblyCode": "FP-MAIN-ASM", "assemblyName": "防火隔断主框架构件", "assemblyVersion": 1, "instanceName": "主框架系统", "quantity": 1, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "parameterValues": {"frame_width": 100, "frame_depth": 140, "material_grade": "304SS", "surface_finish": "brushed"}, "optional": false, "alternatives": [], "properties": {"fire_rating": "A", "corrosion_resistance": "high", "load_bearing": "heavy_duty"}, "parameters": [{"id": "param_fp_frame_width", "name": "frame_width", "displayName": "框架宽度", "type": "dimension", "inputType": "select", "unit": "mm", "defaultValue": 100, "required": true, "options": [{"value": 80, "label": "80mm", "additionalCost": -50}, {"value": 100, "label": "100mm", "additionalCost": 0}, {"value": 120, "label": "120mm", "additionalCost": 80}], "helpText": "隔断框架的宽度规格"}], "constraints": [], "componentInstances": [{"id": "comp_fp_vertical_001", "componentId": "comp_fp_vertical", "componentCode": "FP-VERT-001", "componentName": "不锈钢立柱", "instanceName": "主立柱", "quantity": 2, "position": {"x": 0, "y": 0, "z": 0}, "rotation": {"x": 0, "y": 0, "z": 0}, "parameterValues": {"height": 2400, "profile_section": "100x140x3", "material_grade": "304SS", "surface_finish": "brushed"}, "optional": false, "alternatives": [], "properties": {"materialCategoryId": "cat_stainless_steel_profile", "quantityFormula": "height / 1000 * quantity", "costFormula": "quantity * material_unit_cost * 1.5"}, "parameters": [{"id": "param_fp_vert_height", "name": "height", "displayName": "立柱高度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 2400, "minValue": 2000, "maxValue": 4000, "required": true, "helpText": "隔断立柱的总高度"}]}]}, "productParameters": [{"id": "prod_param_fp_width", "name": "partition_width", "displayName": "隔断宽度", "type": "dimension", "inputType": "number", "unit": "mm", "defaultValue": 3000, "minValue": 1000, "maxValue": 6000, "required": true, "helpText": "防火隔断的总宽度"}], "productConstraints": [{"id": "const_fp_001", "name": "隔断面积约束", "expression": "partition_width * partition_height <= 15000000", "errorMessage": "隔断面积不能超过15平方米", "severity": "error"}], "validationRules": [], "configurationOptions": [], "versionHistory": [], "applications": ["高端办公楼", "酒店", "医院", "机场"], "metadata": {"designStandard": "GB/T12955-2008", "certificationRequired": true, "testReportRequired": true, "qualityLevel": "Premium", "manufacturer": "高端玻璃深加工厂"}, "tags": ["防火隔断", "高端", "A级", "120分钟", "不锈钢框架", "夹胶玻璃"]}]}