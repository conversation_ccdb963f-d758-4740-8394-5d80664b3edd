<template>
  <div class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 组件名称 -->
      <div class="space-y-2">
        <Label for="name" class="required">组件名称</Label>
        <Input
          id="name"
          v-model="localData.name"
          placeholder="请输入组件名称"
          :class="{ 'border-red-500': errors.name }"
          @blur="validateField('name')"
        />
        <p v-if="errors.name" class="text-sm text-red-600">{{ errors.name }}</p>
      </div>

      <!-- 组件编码 -->
      <div class="space-y-2">
        <Label for="code" class="required">组件编码</Label>
        <div class="flex gap-2">
          <Input
            id="code"
            v-model="localData.code"
            placeholder="请输入组件编码"
            :class="{ 'border-red-500': errors.code }"
            @blur="validateField('code')"
          />
          <Button @click="generateCode" variant="outline" size="sm">
            <Shuffle class="w-4 h-4" />
          </Button>
        </div>
        <p v-if="errors.code" class="text-sm text-red-600">{{ errors.code }}</p>
        <p class="text-xs text-gray-500">编码用于唯一标识组件，建议使用字母和数字组合</p>
      </div>
    </div>

    <!-- 组件描述 -->
    <div class="space-y-2">
      <Label for="description">组件描述</Label>
      <Textarea
        id="description"
        v-model="localData.description"
        placeholder="请输入组件描述"
        rows="3"
        :class="{ 'border-red-500': errors.description }"
      />
      <p v-if="errors.description" class="text-sm text-red-600">{{ errors.description }}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 组件类型 -->
      <div class="space-y-2">
        <Label for="componentType" class="required">组件类型</Label>
        <Select v-model="localData.componentType">
          <SelectTrigger id="componentType" :class="{ 'border-red-500': errors.componentType }">
            <SelectValue placeholder="选择组件类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="frame">框料</SelectItem>
            <SelectItem value="glass">玻璃</SelectItem>
            <SelectItem value="hardware">五金</SelectItem>
            <SelectItem value="seal">密封</SelectItem>
            <SelectItem value="other">其他</SelectItem>
          </SelectContent>
        </Select>
        <p v-if="errors.componentType" class="text-sm text-red-600">{{ errors.componentType }}</p>
      </div>

      <!-- 组件状态 -->
      <div class="space-y-2">
        <Label for="status">组件状态</Label>
        <Select v-model="localData.status">
          <SelectTrigger id="status">
            <SelectValue placeholder="选择组件状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="active">活跃</SelectItem>
            <SelectItem value="deprecated">已弃用</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 物料分类 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">物料分类</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="space-y-2">
          <Label for="materialCategory">物料分类</Label>
          <Select v-model="selectedMaterialCategory" @update:model-value="updateMaterialCategory">
            <SelectTrigger id="materialCategory">
              <SelectValue placeholder="选择物料分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem 
                v-for="category in materialCategories" 
                :key="category.id" 
                :value="category.id"
              >
                {{ category.name }} ({{ category.code }})
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div class="space-y-2">
          <Label for="materialCategoryName">分类名称</Label>
          <Input
            id="materialCategoryName"
            v-model="localData.materialCategoryName"
            placeholder="物料分类名称"
            readonly
          />
        </div>
        
        <div class="space-y-2">
          <Label for="materialCategoryCode">分类编码</Label>
          <Input
            id="materialCategoryCode"
            v-model="localData.materialCategoryCode"
            placeholder="物料分类编码"
            readonly
          />
        </div>
      </div>
    </div>

    <!-- 计算公式 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">计算公式</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="quantityFormula" class="required">数量公式</Label>
          <div class="space-y-2">
            <Input
              id="quantityFormula"
              v-model="localData.quantityFormula"
              placeholder="例如: 1 或 width * height / 1000000"
              :class="{ 'border-red-500': errors.quantityFormula }"
              @blur="validateField('quantityFormula')"
            />
            <p class="text-xs text-gray-500">
              用于计算组件数量，可以使用参数名称和数学运算符
            </p>
          </div>
          <p v-if="errors.quantityFormula" class="text-sm text-red-600">{{ errors.quantityFormula }}</p>
        </div>
        
        <div class="space-y-2">
          <Label for="costFormula">成本公式</Label>
          <div class="space-y-2">
            <Input
              id="costFormula"
              v-model="localData.costFormula"
              placeholder="例如: width * height * 0.001"
              :class="{ 'border-red-500': errors.costFormula }"
            />
            <p class="text-xs text-gray-500">
              用于计算组件成本，可以使用参数名称和数学运算符
            </p>
          </div>
          <p v-if="errors.costFormula" class="text-sm text-red-600">{{ errors.costFormula }}</p>
        </div>
      </div>
    </div>

    <!-- 标签和属性 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">标签和属性</h3>
      
      <!-- 标签 -->
      <div class="space-y-2">
        <Label for="tags">标签</Label>
        <div class="space-y-2">
          <div v-if="localData.tags && localData.tags.length > 0" class="flex flex-wrap gap-2">
            <Badge
              v-for="tag in localData.tags"
              :key="tag"
              variant="secondary"
              class="cursor-pointer"
              @click="removeTag(tag)"
            >
              {{ tag }}
              <X class="w-3 h-3 ml-1" />
            </Badge>
          </div>
          <Input
            id="tags"
            v-model="tagInput"
            placeholder="输入标签名称，按回车或逗号添加"
            @keydown.enter="addTag"
            @keydown.comma="addTag"
          />
        </div>
      </div>

      <!-- 可重用性 -->
      <div class="flex items-center space-x-2">
        <Checkbox
          id="reusable"
          v-model:checked="localData.reusable"
        />
        <Label for="reusable">可重用组件</Label>
        <p class="text-xs text-gray-500 ml-2">
          可重用组件可以在多个产品中使用
        </p>
      </div>
    </div>

    <!-- 扩展属性 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">扩展属性</h3>
        <Button @click="addProperty" variant="outline" size="sm">
          <Plus class="w-4 h-4 mr-1" />
          添加属性
        </Button>
      </div>
      
      <div v-if="propertyEntries.length > 0" class="space-y-3">
        <div
          v-for="(entry, index) in propertyEntries"
          :key="index"
          class="flex items-center gap-3 p-3 border rounded-lg"
        >
          <Input
            v-model="entry.key"
            placeholder="属性名"
            class="flex-1"
            @blur="updateProperties"
          />
          <Input
            v-model="entry.value"
            placeholder="属性值"
            class="flex-1"
            @blur="updateProperties"
          />
          <Button @click="removeProperty(index)" variant="ghost" size="sm">
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      <p v-else class="text-sm text-gray-500">
        暂无扩展属性，点击"添加属性"按钮添加自定义属性
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { Shuffle, X, Plus, Trash2 } from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import type { Component } from '@/types/product-structure';

// Props
interface Props {
  modelValue: Partial<Component>;
  errors: Record<string, string>;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: Partial<Component>): void;
  (e: 'validate', errors: Record<string, string>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const localData = ref<Partial<Component>>({ ...props.modelValue });
const tagInput = ref('');
const selectedMaterialCategory = ref('');
const propertyEntries = ref<{ key: string; value: string }[]>([]);

// 物料分类数据
const materialCategories = ref([
  { id: 'aluminum-frame', name: '铝合金框料', code: 'ALF001' },
  { id: 'steel-frame', name: '钢制框料', code: 'STF001' },
  { id: 'tempered-glass', name: '钢化玻璃', code: 'TGL001' },
  { id: 'laminated-glass', name: '夹胶玻璃', code: 'LGL001' },
  { id: 'door-hardware', name: '门五金', code: 'DHW001' },
  { id: 'window-hardware', name: '窗五金', code: 'WHW001' },
  { id: 'rubber-seal', name: '橡胶密封', code: 'RSL001' },
  { id: 'foam-seal', name: '泡沫密封', code: 'FSL001' }
]);

// 计算属性
const errors = computed(() => props.errors);

// 方法
const validateField = (fieldName: string) => {
  const newErrors = { ...errors.value };
  
  switch (fieldName) {
    case 'name':
      if (!localData.value.name?.trim()) {
        newErrors.name = '组件名称不能为空';
      } else {
        delete newErrors.name;
      }
      break;
      
    case 'code':
      if (!localData.value.code?.trim()) {
        newErrors.code = '组件编码不能为空';
      } else if (!/^[A-Za-z0-9_-]+$/.test(localData.value.code)) {
        newErrors.code = '组件编码只能包含字母、数字、下划线和连字符';
      } else {
        delete newErrors.code;
      }
      break;
      
    case 'quantityFormula':
      if (!localData.value.quantityFormula?.trim()) {
        newErrors.quantityFormula = '数量公式不能为空';
      } else {
        delete newErrors.quantityFormula;
      }
      break;
  }
  
  emit('validate', newErrors);
};

const generateCode = () => {
  const prefix = getTypePrefix(localData.value.componentType || 'other');
  const timestamp = Date.now().toString().slice(-6);
  localData.value.code = `${prefix}${timestamp}`;
  validateField('code');
};

const getTypePrefix = (type: string) => {
  const prefixMap: Record<string, string> = {
    frame: 'FR',
    glass: 'GL',
    hardware: 'HW',
    seal: 'SL',
    other: 'OT'
  };
  return prefixMap[type] || 'OT';
};

const updateMaterialCategory = (categoryId: string) => {
  const category = materialCategories.value.find(c => c.id === categoryId);
  if (category) {
    localData.value.materialCategoryId = category.id;
    localData.value.materialCategoryName = category.name;
    localData.value.materialCategoryCode = category.code;
  }
};

const addTag = () => {
  const tag = tagInput.value.trim().replace(',', '');
  if (tag && localData.value.tags && !localData.value.tags.includes(tag)) {
    localData.value.tags.push(tag);
    tagInput.value = '';
  } else if (tag && !localData.value.tags) {
    localData.value.tags = [tag];
    tagInput.value = '';
  }
};

const removeTag = (tag: string) => {
  if (localData.value.tags) {
    const index = localData.value.tags.indexOf(tag);
    if (index > -1) {
      localData.value.tags.splice(index, 1);
    }
  }
};

const addProperty = () => {
  propertyEntries.value.push({ key: '', value: '' });
};

const removeProperty = (index: number) => {
  propertyEntries.value.splice(index, 1);
  updateProperties();
};

const updateProperties = () => {
  const properties: Record<string, any> = {};
  propertyEntries.value.forEach(entry => {
    if (entry.key.trim() && entry.value.trim()) {
      properties[entry.key.trim()] = entry.value.trim();
    }
  });
  localData.value.properties = properties;
};

const initializePropertyEntries = () => {
  if (localData.value.properties) {
    propertyEntries.value = Object.entries(localData.value.properties).map(([key, value]) => ({
      key,
      value: String(value)
    }));
  }
};

// 防止循环更新的标志
const isUpdating = ref(false);

// 监听器
watch(localData, (newValue) => {
  if (isUpdating.value) return;
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  if (isUpdating.value) return;

  isUpdating.value = true;
  localData.value = { ...newValue };

  // 更新物料分类选择
  if (newValue.materialCategoryId) {
    selectedMaterialCategory.value = newValue.materialCategoryId;
  }

  // 初始化属性条目
  initializePropertyEntries();

  // 使用nextTick确保更新完成后重置标志
  nextTick(() => {
    isUpdating.value = false;
  });
}, { deep: true });

// 生命周期
onMounted(() => {
  // 初始化数据
  if (props.modelValue.materialCategoryId) {
    selectedMaterialCategory.value = props.modelValue.materialCategoryId;
  }
  
  initializePropertyEntries();
  
  // 初始验证
  validateField('name');
  validateField('code');
  validateField('quantityFormula');
});
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
