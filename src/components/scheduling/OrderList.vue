<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useSchedulingStore } from '@/stores/scheduling';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Check, Clock } from 'lucide-vue-next';

const schedulingStore = useSchedulingStore();
const selectedOrders = ref<Set<string>>(new Set());

onMounted(() => {
  schedulingStore.fetchUnscheduledOrders();
});

function toggleOrderSelection(orderId: string) {
  if (selectedOrders.value.has(orderId)) {
    selectedOrders.value.delete(orderId);
  } else {
    selectedOrders.value.add(orderId);
  }
}

function handleScheduleClick() {
  if (selectedOrders.value.size > 0) {
    schedulingStore.runIntelligentScheduling(Array.from(selectedOrders.value));
    selectedOrders.value.clear();
  } else {
    // Optionally, show a notification to select orders first
    alert('请至少选择一个订单进行排产。');
  }
}

function getPriorityVariant(priority: string): 'destructive' | 'secondary' | 'outline' {
  switch (priority) {
    case 'urgent':
      return 'destructive';
    case 'normal':
      return 'secondary';
    case 'low':
      return 'outline';
    default:
      return 'secondary';
  }
}
</script>

<template>
  <div class="space-y-6">
    <Card>
      <CardHeader>
        <CardTitle>待处理订单 ({{ schedulingStore.pendingOrderCount }})</CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="schedulingStore.isLoading" class="text-center text-gray-500">
          正在加载订单...
        </div>
        <div v-else-if="schedulingStore.error" class="text-center text-red-500">
          {{ schedulingStore.error }}
        </div>
        <div v-else-if="schedulingStore.unscheduledOrders.length === 0" class="text-center text-gray-500">
          没有待处理的订单。
        </div>
        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <Card 
            v-for="order in schedulingStore.unscheduledOrders" 
            :key="order.id"
            :class="['cursor-pointer transition-all', selectedOrders.has(order.id) ? 'ring-2 ring-primary' : '']"
            @click="toggleOrderSelection(order.id)"
          >
            <CardHeader class="flex flex-row items-center justify-between pb-2">
              <CardTitle class="text-sm font-medium">{{ order.id }}</CardTitle>
              <Badge :variant="getPriorityVariant(order.priority)" class="capitalize">
                <AlertTriangle v-if="order.priority === 'urgent'" class="h-3 w-3 mr-1" />
                {{ order.priority }}
              </Badge>
            </CardHeader>
            <CardContent>
              <div class="text-lg font-bold">{{ order.customer }}</div>
              <p class="text-xs text-muted-foreground">{{ order.product }}</p>
              <div class="mt-4 flex items-center text-xs text-muted-foreground">
                <Clock class="h-3 w-3 mr-1" />
                <span>交期: {{ order.deadline }}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>排产操作</CardTitle>
      </CardHeader>
      <CardContent class="flex flex-col items-center justify-center space-y-4 p-8">
         <div class="text-sm text-muted-foreground mb-2">
          已选择 {{ selectedOrders.size }} 个订单
        </div>
        <Button size="lg" @click="handleScheduleClick" :disabled="selectedOrders.size === 0">
          <Check class="mr-2 h-5 w-5" />
          一键智能排产
        </Button>
        <p class="text-xs text-muted-foreground">系统将基于材料利用率和交期进行智能优化</p>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
/* Additional styles can go here */
</style>