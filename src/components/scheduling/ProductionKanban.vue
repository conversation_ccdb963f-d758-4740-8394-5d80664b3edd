<script setup lang="ts">
import { useSchedulingStore } from '@/stores/scheduling';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const schedulingStore = useSchedulingStore();

// A simple representation of the timeline. In a real app, this would be more dynamic.
const timeSlots = ['今天', '明天', '后天'];
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>生产计划看板 (瓶颈资源: 钢化炉)</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div v-for="slot in timeSlots" :key="slot" class="bg-muted/50 p-4 rounded-lg">
          <h3 class="font-semibold mb-4 text-center">{{ slot }}</h3>
          <div class="space-y-3">
            <template v-if="schedulingStore.productionPlan.filter(p => p.date === slot).length > 0">
              <Card 
                v-for="batch in schedulingStore.productionPlan.filter(p => p.date === slot)" 
                :key="batch.id"
                class="bg-background"
              >
                <CardContent class="p-3">
                  <p class="font-semibold text-sm">{{ batch.id }}</p>
                  <p class="text-xs text-muted-foreground">
                    订单: {{ batch.orders.join(', ') }}
                  </p>
                  <p class="text-xs text-muted-foreground">
                    时间: {{ batch.startTime }} - {{ batch.endTime }}
                  </p>
                </CardContent>
              </Card>
            </template>
            <div v-else class="text-center text-sm text-muted-foreground py-8">
              无生产计划
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
/* Styles for the production kanban component */
</style>
