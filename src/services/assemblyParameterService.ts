/**
 * 构件参数服务
 * 
 * 管理构件参数的定义、继承、传播和约束
 */

import type {
  AssemblyParameter,
  ComponentInstance,
  ParameterPropagationRule,
  ParameterValidationResult,
  AssemblyConstraint
} from '@/types/product-structure';

/**
 * 参数影响分析结果
 */
export interface ParameterImpactAnalysis {
  /** 受影响的组件实例 */
  affectedInstances: string[];
  /** 参数传播链 */
  propagationChain: ParameterPropagationChain[];
  /** 约束冲突 */
  constraintConflicts: ConstraintConflict[];
}

/**
 * 参数传播链
 */
export interface ParameterPropagationChain {
  /** 源参数 */
  sourceParameter: string;
  /** 目标实例 */
  targetInstance: string;
  /** 目标参数 */
  targetParameter: string;
  /** 传播类型 */
  propagationType: 'direct' | 'formula' | 'mapping';
  /** 传播值 */
  propagatedValue: any;
}

/**
 * 约束冲突
 */
export interface ConstraintConflict {
  /** 约束ID */
  constraintId: string;
  /** 冲突类型 */
  conflictType: 'range' | 'dependency' | 'compatibility';
  /** 冲突描述 */
  description: string;
  /** 建议解决方案 */
  suggestedSolution?: string;
}

/**
 * 构件参数服务类
 */
export class AssemblyParameterService {

  /**
   * 创建构件参数
   */
  createAssemblyParameter(config: Partial<AssemblyParameter>): AssemblyParameter {
    return {
      id: config.id || `ap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: config.name || '',
      displayName: config.displayName || config.name || '',
      type: config.type || 'string',
      category: config.category || 'other',
      defaultValue: config.defaultValue,
      required: config.required || false,
      description: config.description,
      unit: config.unit,
      range: config.range,
      options: config.options,
      formula: config.formula,
      affectedInstances: config.affectedInstances || [],
      propagationRules: config.propagationRules || []
    };
  }

  /**
   * 更新构件参数
   */
  updateAssemblyParameter(
    parameter: AssemblyParameter,
    updates: Partial<AssemblyParameter>
  ): AssemblyParameter {
    return {
      ...parameter,
      ...updates,
      id: parameter.id // 确保ID不被修改
    };
  }

  /**
   * 计算参数值
   */
  calculateParameterValue(
    parameter: AssemblyParameter,
    context: Record<string, any> = {}
  ): any {
    if (parameter.type === 'formula' && parameter.formula) {
      try {
        return this.evaluateFormula(parameter.formula, context);
      } catch (error) {
        console.warn(`参数公式计算失败: ${parameter.formula}`, error);
        return parameter.defaultValue;
      }
    }
    
    return context[parameter.name] !== undefined ? context[parameter.name] : parameter.defaultValue;
  }

  /**
   * 验证参数值
   */
  validateParameterValue(
    parameter: AssemblyParameter,
    value: any
  ): ParameterValidationResult {
    const result: ParameterValidationResult = {
      isValid: true
    };

    // 必需参数检查
    if (parameter.required && (value === undefined || value === null || value === '')) {
      result.isValid = false;
      result.errorMessage = `参数 ${parameter.displayName} 是必需的`;
      return result;
    }

    // 类型检查
    switch (parameter.type) {
      case 'number':
        if (value !== undefined && value !== null) {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            result.isValid = false;
            result.errorMessage = `参数 ${parameter.displayName} 必须是数值`;
            return result;
          }

          // 范围检查
          if (parameter.range) {
            if (parameter.range.min !== undefined && numValue < parameter.range.min) {
              result.isValid = false;
              result.errorMessage = `参数 ${parameter.displayName} 不能小于 ${parameter.range.min}`;
              return result;
            }
            if (parameter.range.max !== undefined && numValue > parameter.range.max) {
              result.isValid = false;
              result.errorMessage = `参数 ${parameter.displayName} 不能大于 ${parameter.range.max}`;
              return result;
            }
          }
        }
        break;

      case 'select':
        if (parameter.options && value !== undefined) {
          const validOptions = parameter.options.map(opt => opt.value);
          if (!validOptions.includes(value)) {
            result.isValid = false;
            result.errorMessage = `参数 ${parameter.displayName} 的值不在有效选项中`;
            return result;
          }
        }
        break;

      case 'boolean':
        if (value !== undefined && typeof value !== 'boolean') {
          result.isValid = false;
          result.errorMessage = `参数 ${parameter.displayName} 必须是布尔值`;
          return result;
        }
        break;
    }

    return result;
  }

  /**
   * 分析参数影响
   */
  analyzeParameterImpact(
    parameter: AssemblyParameter,
    newValue: any,
    componentInstances: ComponentInstance[],
    assemblyConstraints: AssemblyConstraint[]
  ): ParameterImpactAnalysis {
    const affectedInstances = parameter.affectedInstances;
    const propagationChain: ParameterPropagationChain[] = [];
    const constraintConflicts: ConstraintConflict[] = [];

    // 分析参数传播
    parameter.propagationRules.forEach(rule => {
      const targetInstance = componentInstances.find(inst => inst.id === rule.targetInstanceId);
      if (targetInstance) {
        let propagatedValue = newValue;

        // 根据传播类型计算传播值
        switch (rule.propagationType) {
          case 'direct':
            propagatedValue = newValue;
            break;
          case 'formula':
            if (rule.expression) {
              try {
                propagatedValue = this.evaluateFormula(rule.expression, { [parameter.name]: newValue });
              } catch (error) {
                console.warn(`传播公式计算失败: ${rule.expression}`, error);
              }
            }
            break;
          case 'mapping':
            if (rule.mapping && rule.mapping[newValue]) {
              propagatedValue = rule.mapping[newValue];
            }
            break;
        }

        propagationChain.push({
          sourceParameter: parameter.name,
          targetInstance: rule.targetInstanceId,
          targetParameter: rule.targetParameterName,
          propagationType: rule.propagationType,
          propagatedValue
        });
      }
    });

    // 分析约束冲突
    assemblyConstraints.forEach(constraint => {
      if (constraint.enabled) {
        try {
          const context = { [parameter.name]: newValue };
          const constraintResult = this.evaluateConstraint(constraint.expression, context);
          
          if (!constraintResult) {
            constraintConflicts.push({
              constraintId: constraint.id,
              conflictType: 'dependency',
              description: constraint.errorMessage,
              suggestedSolution: constraint.autoFix?.fixMessage
            });
          }
        } catch (error) {
          console.warn(`约束检查失败: ${constraint.expression}`, error);
        }
      }
    });

    return {
      affectedInstances,
      propagationChain,
      constraintConflicts
    };
  }

  /**
   * 应用参数传播
   */
  applyParameterPropagation(
    sourceParameter: AssemblyParameter,
    sourceValue: any,
    componentInstances: ComponentInstance[]
  ): ComponentInstance[] {
    return componentInstances.map(instance => {
      const applicableRules = sourceParameter.propagationRules.filter(
        rule => rule.targetInstanceId === instance.id
      );

      if (applicableRules.length === 0) {
        return instance;
      }

      const updatedOverrides = { ...instance.parameterConfig.parameterOverrides };

      applicableRules.forEach(rule => {
        let propagatedValue = sourceValue;

        switch (rule.propagationType) {
          case 'direct':
            propagatedValue = sourceValue;
            break;
          case 'formula':
            if (rule.expression) {
              try {
                propagatedValue = this.evaluateFormula(rule.expression, { [sourceParameter.name]: sourceValue });
              } catch (error) {
                console.warn(`传播公式计算失败: ${rule.expression}`, error);
                return;
              }
            }
            break;
          case 'mapping':
            if (rule.mapping && rule.mapping[sourceValue]) {
              propagatedValue = rule.mapping[sourceValue];
            } else {
              return; // 没有映射值，跳过传播
            }
            break;
        }

        updatedOverrides[rule.targetParameterName] = propagatedValue;
      });

      return {
        ...instance,
        parameterConfig: {
          ...instance.parameterConfig,
          parameterOverrides: updatedOverrides
        },
        updatedAt: new Date().toISOString()
      };
    });
  }

  /**
   * 获取参数依赖关系
   */
  getParameterDependencies(
    parameters: AssemblyParameter[]
  ): Record<string, string[]> {
    const dependencies: Record<string, string[]> = {};

    parameters.forEach(param => {
      dependencies[param.name] = [];

      // 从公式中提取依赖
      if (param.type === 'formula' && param.formula) {
        const deps = this.extractDependenciesFromFormula(param.formula, parameters);
        dependencies[param.name] = deps;
      }

      // 从传播规则中提取依赖
      param.propagationRules.forEach(rule => {
        if (rule.expression) {
          const deps = this.extractDependenciesFromFormula(rule.expression, parameters);
          dependencies[param.name] = [...new Set([...dependencies[param.name], ...deps])];
        }
      });
    });

    return dependencies;
  }

  /**
   * 检查循环依赖
   */
  checkCircularDependencies(parameters: AssemblyParameter[]): string[] {
    const dependencies = this.getParameterDependencies(parameters);
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[] = [];

    const dfs = (param: string, path: string[]): void => {
      if (recursionStack.has(param)) {
        cycles.push(`循环依赖: ${path.join(' -> ')} -> ${param}`);
        return;
      }

      if (visited.has(param)) {
        return;
      }

      visited.add(param);
      recursionStack.add(param);

      const deps = dependencies[param] || [];
      deps.forEach(dep => {
        dfs(dep, [...path, param]);
      });

      recursionStack.delete(param);
    };

    Object.keys(dependencies).forEach(param => {
      if (!visited.has(param)) {
        dfs(param, []);
      }
    });

    return cycles;
  }

  /**
   * 计算参数值（考虑依赖关系）
   */
  calculateParameterValues(
    parameters: AssemblyParameter[],
    inputValues: Record<string, any> = {}
  ): Record<string, any> {
    const dependencies = this.getParameterDependencies(parameters);
    const calculated = { ...inputValues };
    const calculating = new Set<string>();

    const calculate = (paramName: string): any => {
      if (calculated.hasOwnProperty(paramName)) {
        return calculated[paramName];
      }

      if (calculating.has(paramName)) {
        throw new Error(`循环依赖检测到: ${paramName}`);
      }

      const param = parameters.find(p => p.name === paramName);
      if (!param) {
        return undefined;
      }

      calculating.add(paramName);

      try {
        if (param.type === 'formula' && param.formula) {
          // 先计算依赖参数
          const deps = dependencies[paramName] || [];
          const context: Record<string, any> = {};
          
          deps.forEach(dep => {
            context[dep] = calculate(dep);
          });

          calculated[paramName] = this.evaluateFormula(param.formula, context);
        } else {
          calculated[paramName] = param.defaultValue;
        }
      } catch (error) {
        console.warn(`参数计算失败: ${paramName}`, error);
        calculated[paramName] = param.defaultValue;
      }

      calculating.delete(paramName);
      return calculated[paramName];
    };

    parameters.forEach(param => {
      if (!calculated.hasOwnProperty(param.name)) {
        calculate(param.name);
      }
    });

    return calculated;
  }

  /**
   * 从公式中提取依赖参数
   */
  private extractDependenciesFromFormula(
    formula: string,
    parameters: AssemblyParameter[]
  ): string[] {
    const paramNames = parameters.map(p => p.name);
    const dependencies: string[] = [];

    paramNames.forEach(name => {
      const regex = new RegExp(`\\b${name}\\b`, 'g');
      if (regex.test(formula)) {
        dependencies.push(name);
      }
    });

    return dependencies;
  }

  /**
   * 计算公式
   */
  private evaluateFormula(formula: string, variables: Record<string, any>): any {
    let expression = formula;
    
    // 替换变量
    Object.entries(variables).forEach(([key, value]) => {
      expression = expression.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
    });
    
    try {
      // 注意：在生产环境中不应使用 eval，应使用专门的表达式解析库
      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      throw new Error(`公式计算错误: ${formula}`);
    }
  }

  /**
   * 计算约束表达式
   */
  private evaluateConstraint(expression: string, variables: Record<string, any>): boolean {
    try {
      const result = this.evaluateFormula(expression, variables);
      return Boolean(result);
    } catch (error) {
      console.warn(`约束计算失败: ${expression}`, error);
      return true; // 计算失败时假设约束满足
    }
  }
}

// 导出服务实例
export const assemblyParameterService = new AssemblyParameterService();
