/**
 * 构件管理服务（重构版）
 *
 * 提供构件的CRUD操作、搜索筛选、批量操作等功能
 * 重构后突出构件作为"组件集合体"的特性
 */

import type {
  Assembly,
  AssemblyType,
  ComponentInstance,
  AssemblyInstance,
  AssemblyParameter,
  AssemblyConstraint,
  AssemblyProcess,
  QualityRequirement,
  LifecycleStatus,
  ValidationResult
} from '@/types/product-structure';

import { componentInstanceService, type ComponentInstanceCreateConfig } from './componentInstanceService';
import { assemblyParameterService } from './assemblyParameterService';
import { assemblyValidationService, type AssemblyValidationResult } from './assemblyValidationService';

/**
 * 构件筛选条件
 */
export interface AssemblyFilters {
  /** 搜索关键词 */
  search?: string;
  /** 构件类型 */
  assemblyType?: AssemblyType[];
  /** 构件状态 */
  status?: LifecycleStatus[];
  /** 标签 */
  tags?: string[];
  /** 创建时间范围 */
  createdDateRange?: [string, string];
  /** 更新时间范围 */
  updatedDateRange?: [string, string];
  /** 是否包含特定组件 */
  containsComponent?: string;
}

/**
 * 构件排序选项
 */
export interface AssemblySortOptions {
  field: 'name' | 'code' | 'assemblyType' | 'createdAt' | 'updatedAt';
  order: 'asc' | 'desc';
}

/**
 * 构件统计信息
 */
export interface AssemblyStatistics {
  total: number;
  byType: Record<AssemblyType, number>;
  byStatus: Record<LifecycleStatus, number>;
  recentlyCreated: number;
  recentlyUpdated: number;
}

/**
 * 批量操作类型
 */
export type BatchOperationType = 'updateStatus' | 'addTags' | 'removeTags' | 'delete';

/**
 * 批量操作请求
 */
export interface BatchOperationRequest {
  type: BatchOperationType;
  assemblyIds: string[];
  data?: any;
}

/**
 * 构件服务类
 */
export class AssemblyService {
  private assemblies: Assembly[] = [];
  private nextId = 1000;
  private dataLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  constructor() {
    // 不在构造函数中加载数据，而是在需要时加载
  }

  /**
   * 确保数据已加载
   */
  private async ensureDataLoaded(): Promise<void> {
    if (this.dataLoaded) {
      return;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this.loadMockData();
    await this.loadingPromise;
  }

  /**
   * 加载Mock数据
   */
  private async loadMockData(): Promise<void> {
    try {
      const response = await fetch('/mock/product/assemblies.json');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      this.assemblies = data.assemblies || [];
      this.nextId = Math.max(...this.assemblies.map(a => parseInt(a.id.replace('asm_', '')))) + 1;
      this.dataLoaded = true;
    } catch (error) {
      console.error('加载构件数据失败:', error);
      this.assemblies = [];
      this.dataLoaded = true; // 即使失败也标记为已加载，避免重复尝试
    }
  }

  /**
   * 获取构件列表
   */
  async getAssemblies(
    filters?: AssemblyFilters,
    sort?: AssemblySortOptions,
    pagination?: { page: number; pageSize: number }
  ): Promise<{
    assemblies: Assembly[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    await this.ensureDataLoaded();
    let filteredAssemblies = [...this.assemblies];

    // 应用筛选条件
    if (filters) {
      filteredAssemblies = this.applyFilters(filteredAssemblies, filters);
    }

    // 应用排序
    if (sort) {
      filteredAssemblies = this.applySorting(filteredAssemblies, sort);
    }

    const total = filteredAssemblies.length;

    // 应用分页
    if (pagination) {
      const startIndex = (pagination.page - 1) * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      filteredAssemblies = filteredAssemblies.slice(startIndex, endIndex);
    }

    return {
      assemblies: filteredAssemblies,
      total,
      page: pagination?.page || 1,
      pageSize: pagination?.pageSize || total
    };
  }

  /**
   * 根据ID获取构件
   */
  async getAssemblyById(id: string): Promise<Assembly | null> {
    await this.ensureDataLoaded();
    return this.assemblies.find(a => a.id === id) || null;
  }

  /**
   * 创建构件
   */
  async createAssembly(assemblyData: Partial<Assembly>): Promise<Assembly> {
    await this.ensureDataLoaded();
    const now = new Date().toISOString();
    const assembly: Assembly = {
      id: `asm_${this.nextId++}`,
      code: assemblyData.code || `ASM${String(this.nextId).padStart(3, '0')}`,
      name: assemblyData.name || '新构件',
      description: assemblyData.description || '',
      version: 1,
      status: assemblyData.status || 'draft',
      createdAt: now,
      updatedAt: now,
      createdBy: 'current-user',
      updatedBy: 'current-user',
      assemblyType: assemblyData.assemblyType || 'complete_assembly',
      componentInstances: assemblyData.componentInstances || [],
      subAssemblies: assemblyData.subAssemblies || [],
      assemblyParameters: assemblyData.assemblyParameters || [],
      assemblyConstraints: assemblyData.assemblyConstraints || [],
      assemblyProcess: assemblyData.assemblyProcess || this.createDefaultProcess(),
      qualityRequirements: assemblyData.qualityRequirements || [],
      properties: assemblyData.properties || {},
      tags: assemblyData.tags || []
    };

    this.assemblies.push(assembly);
    return assembly;
  }

  /**
   * 更新构件
   */
  async updateAssembly(id: string, updates: Partial<Assembly>): Promise<Assembly> {
    await this.ensureDataLoaded();
    const index = this.assemblies.findIndex(a => a.id === id);
    if (index === -1) {
      throw new Error(`构件 ${id} 不存在`);
    }

    const assembly = this.assemblies[index];
    const updatedAssembly: Assembly = {
      ...assembly,
      ...updates,
      id: assembly.id, // 确保ID不被修改
      updatedAt: new Date().toISOString(),
      updatedBy: 'current-user'
    };

    this.assemblies[index] = updatedAssembly;
    return updatedAssembly;
  }

  /**
   * 删除构件
   */
  async deleteAssembly(id: string): Promise<void> {
    await this.ensureDataLoaded();
    const index = this.assemblies.findIndex(a => a.id === id);
    if (index === -1) {
      throw new Error(`构件 ${id} 不存在`);
    }

    this.assemblies.splice(index, 1);
  }

  /**
   * 复制构件
   */
  async duplicateAssembly(id: string, newName?: string): Promise<Assembly> {
    await this.ensureDataLoaded();
    const original = await this.getAssemblyById(id);
    if (!original) {
      throw new Error(`构件 ${id} 不存在`);
    }

    const duplicated = {
      ...original,
      name: newName || `${original.name}_副本`,
      code: `${original.code}_COPY`,
      status: 'draft' as LifecycleStatus
    };

    delete (duplicated as any).id;
    return this.createAssembly(duplicated);
  }

  /**
   * 批量操作
   */
  async batchOperation(request: BatchOperationRequest): Promise<{
    success: string[];
    failed: string[];
    errors: Record<string, string>;
  }> {
    await this.ensureDataLoaded();
    const result = {
      success: [] as string[],
      failed: [] as string[],
      errors: {} as Record<string, string>
    };

    for (const id of request.assemblyIds) {
      try {
        switch (request.type) {
          case 'updateStatus':
            await this.updateAssembly(id, { status: request.data.status });
            break;
          case 'addTags':
            const assembly = await this.getAssemblyById(id);
            if (assembly) {
              const newTags = [...new Set([...assembly.tags, ...request.data.tags])];
              await this.updateAssembly(id, { tags: newTags });
            }
            break;
          case 'removeTags':
            const assemblyToUpdate = await this.getAssemblyById(id);
            if (assemblyToUpdate) {
              const filteredTags = assemblyToUpdate.tags.filter(
                tag => !request.data.tags.includes(tag)
              );
              await this.updateAssembly(id, { tags: filteredTags });
            }
            break;
          case 'delete':
            await this.deleteAssembly(id);
            break;
        }
        result.success.push(id);
      } catch (error) {
        result.failed.push(id);
        result.errors[id] = error instanceof Error ? error.message : '未知错误';
      }
    }

    return result;
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<AssemblyStatistics> {
    await this.ensureDataLoaded();
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const byType = this.assemblies.reduce((acc, assembly) => {
      acc[assembly.assemblyType] = (acc[assembly.assemblyType] || 0) + 1;
      return acc;
    }, {} as Record<AssemblyType, number>);

    const byStatus = this.assemblies.reduce((acc, assembly) => {
      acc[assembly.status] = (acc[assembly.status] || 0) + 1;
      return acc;
    }, {} as Record<LifecycleStatus, number>);

    const recentlyCreated = this.assemblies.filter(
      assembly => new Date(assembly.createdAt) > sevenDaysAgo
    ).length;

    const recentlyUpdated = this.assemblies.filter(
      assembly => new Date(assembly.updatedAt) > sevenDaysAgo
    ).length;

    return {
      total: this.assemblies.length,
      byType,
      byStatus,
      recentlyCreated,
      recentlyUpdated
    };
  }

  // ============================================================================
  // 重构后的新方法 - 突出构件作为"组件集合体"的特性
  // ============================================================================

  /**
   * 添加组件实例到构件
   */
  async addComponentInstance(
    assemblyId: string,
    config: ComponentInstanceCreateConfig
  ): Promise<ComponentInstance> {
    await this.ensureDataLoaded();
    const assembly = await this.getAssemblyById(assemblyId);
    if (!assembly) {
      throw new Error(`构件 ${assemblyId} 不存在`);
    }

    // 创建组件实例
    const instance = await componentInstanceService.createComponentInstance(
      config,
      assembly.assemblyParameters || []
    );

    // 更新构件
    const updatedAssembly = {
      ...assembly,
      componentInstances: [...assembly.componentInstances, instance],
      updatedAt: new Date().toISOString(),
      updatedBy: 'current-user'
    };

    await this.updateAssembly(assemblyId, updatedAssembly);
    return instance;
  }

  /**
   * 更新组件实例
   */
  async updateComponentInstance(
    assemblyId: string,
    instanceId: string,
    updates: Partial<ComponentInstance>
  ): Promise<ComponentInstance> {
    await this.ensureDataLoaded();
    const assembly = await this.getAssemblyById(assemblyId);
    if (!assembly) {
      throw new Error(`构件 ${assemblyId} 不存在`);
    }

    const instanceIndex = assembly.componentInstances.findIndex(i => i.id === instanceId);
    if (instanceIndex === -1) {
      throw new Error(`组件实例 ${instanceId} 不存在`);
    }

    const updatedInstance = {
      ...assembly.componentInstances[instanceIndex],
      ...updates,
      id: instanceId, // 确保ID不被修改
      updatedAt: new Date().toISOString()
    };

    const updatedInstances = [...assembly.componentInstances];
    updatedInstances[instanceIndex] = updatedInstance;

    await this.updateAssembly(assemblyId, {
      componentInstances: updatedInstances
    });

    return updatedInstance;
  }

  /**
   * 移除组件实例
   */
  async removeComponentInstance(assemblyId: string, instanceId: string): Promise<void> {
    await this.ensureDataLoaded();
    const assembly = await this.getAssemblyById(assemblyId);
    if (!assembly) {
      throw new Error(`构件 ${assemblyId} 不存在`);
    }

    const updatedInstances = assembly.componentInstances.filter(i => i.id !== instanceId);

    await this.updateAssembly(assemblyId, {
      componentInstances: updatedInstances
    });
  }

  /**
   * 更新构件参数
   */
  async updateAssemblyParameters(
    assemblyId: string,
    parameters: AssemblyParameter[]
  ): Promise<Assembly> {
    await this.ensureDataLoaded();
    const assembly = await this.getAssemblyById(assemblyId);
    if (!assembly) {
      throw new Error(`构件 ${assemblyId} 不存在`);
    }

    // 应用参数传播
    const updatedInstances = this.applyParameterPropagation(
      parameters,
      assembly.componentInstances
    );

    return this.updateAssembly(assemblyId, {
      assemblyParameters: parameters,
      componentInstances: updatedInstances
    });
  }

  /**
   * 验证构件
   */
  async validateAssembly(assemblyId: string): Promise<AssemblyValidationResult> {
    await this.ensureDataLoaded();
    const assembly = await this.getAssemblyById(assemblyId);
    if (!assembly) {
      throw new Error(`构件 ${assemblyId} 不存在`);
    }

    return assemblyValidationService.validateAssembly(assembly);
  }

  /**
   * 应用参数传播
   */
  private applyParameterPropagation(
    parameters: AssemblyParameter[],
    instances: ComponentInstance[]
  ): ComponentInstance[] {
    let updatedInstances = [...instances];

    parameters.forEach(param => {
      if (param.propagationRules.length > 0) {
        const paramValue = assemblyParameterService.calculateParameterValue(param);
        updatedInstances = assemblyParameterService.applyParameterPropagation(
          param,
          paramValue,
          updatedInstances
        );
      }
    });

    return updatedInstances;
  }

  /**
   * 应用筛选条件
   */
  private applyFilters(assemblies: Assembly[], filters: AssemblyFilters): Assembly[] {
    return assemblies.filter(assembly => {
      // 搜索关键词
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          assembly.name.toLowerCase().includes(searchLower) ||
          assembly.code.toLowerCase().includes(searchLower) ||
          (assembly.description && assembly.description.toLowerCase().includes(searchLower)) ||
          assembly.tags.some(tag => tag.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // 构件类型
      if (filters.assemblyType && filters.assemblyType.length > 0) {
        if (!filters.assemblyType.includes(assembly.assemblyType)) return false;
      }

      // 状态
      if (filters.status && filters.status.length > 0) {
        if (!filters.status.includes(assembly.status)) return false;
      }

      // 标签
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => assembly.tags.includes(tag));
        if (!hasMatchingTag) return false;
      }

      // 创建时间范围
      if (filters.createdDateRange) {
        const createdAt = new Date(assembly.createdAt);
        const [start, end] = filters.createdDateRange.map(d => new Date(d));
        if (createdAt < start || createdAt > end) return false;
      }

      // 更新时间范围
      if (filters.updatedDateRange) {
        const updatedAt = new Date(assembly.updatedAt);
        const [start, end] = filters.updatedDateRange.map(d => new Date(d));
        if (updatedAt < start || updatedAt > end) return false;
      }

      // 包含特定组件
      if (filters.containsComponent) {
        const hasComponent = assembly.componentInstances.some(
          instance => instance.componentId === filters.containsComponent
        );
        if (!hasComponent) return false;
      }

      return true;
    });
  }

  /**
   * 应用排序
   */
  private applySorting(assemblies: Assembly[], sort: AssemblySortOptions): Assembly[] {
    return assemblies.sort((a, b) => {
      let aValue: any = a[sort.field];
      let bValue: any = b[sort.field];

      // 处理日期字段
      if (sort.field === 'createdAt' || sort.field === 'updatedAt') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      // 处理字符串字段
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sort.order === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.order === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  /**
   * 创建默认装配工艺
   */
  private createDefaultProcess(): AssemblyProcess {
    return {
      id: `proc_${Date.now()}`,
      processName: '默认装配工艺',
      description: '标准装配工艺流程',
      steps: [],
      totalEstimatedTime: 0,
      requiredSkills: [],
      safetyRequirements: []
    };
  }
}

// 导出服务实例
export const assemblyService = new AssemblyService();
