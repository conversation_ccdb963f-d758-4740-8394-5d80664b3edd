/**
 * 构件参数模板服务
 * 
 * 提供构件类型的参数模板、智能参数推荐、参数完整性检查等功能
 */

import type {
  AssemblyType,
  ComponentParameter,
  Component,
  ComponentInstance,
  AssemblyParameter
} from '@/types/product-structure';

/** 参数分组 */
export interface ParameterGroup {
  category: string;
  displayName: string;
  description?: string;
  parameters: ComponentParameter[];
}

/** 构件参数模板 */
export interface AssemblyParameterTemplate {
  assemblyType: AssemblyType;
  name: string;
  description: string;
  parameterGroups: ParameterGroup[];
  commonComponents?: string[]; // 常用组件类型
  requiredParameters: string[]; // 必需参数名称
}

/** 参数完整性检查结果 */
export interface ParameterCompletenessResult {
  isComplete: boolean;
  missingParameters: ComponentParameter[];
  suggestions: ComponentParameter[];
  conflicts: ParameterConflict[];
}

/** 参数冲突 */
export interface ParameterConflict {
  parameterName: string;
  conflictType: 'duplicate' | 'type_mismatch' | 'range_conflict';
  description: string;
  suggestions: string[];
}

/**
 * 构件参数模板服务类
 */
export class AssemblyTemplateService {
  
  private templates: Map<AssemblyType, AssemblyParameterTemplate> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  /**
   * 初始化参数模板
   */
  private initializeTemplates() {
    // 防火窗框构件模板
    this.templates.set('frame_assembly', {
      assemblyType: 'frame_assembly',
      name: '防火窗框构件',
      description: '防火窗的框架部分，包含立柱、横梁、密封胶条等组件',
      parameterGroups: [
        {
          category: 'dimensions',
          displayName: '尺寸参数',
          description: '构件的整体尺寸和几何参数',
          parameters: [
            {
              id: 'overall_width',
              name: 'overallWidth',
              displayName: '总宽度',
              type: 'number',
              defaultValue: 1200,
              required: true,
              description: '窗框的总宽度',
              unit: 'mm',
              category: 'dimensions',
              range: { min: 300, max: 3000 }
            },
            {
              id: 'overall_height',
              name: 'overallHeight',
              displayName: '总高度',
              type: 'number',
              defaultValue: 1500,
              required: true,
              description: '窗框的总高度',
              unit: 'mm',
              category: 'dimensions',
              range: { min: 300, max: 3000 }
            },
            {
              id: 'frame_depth',
              name: 'frameDepth',
              displayName: '框架深度',
              type: 'number',
              defaultValue: 120,
              required: true,
              description: '窗框的深度尺寸',
              unit: 'mm',
              category: 'dimensions',
              range: { min: 80, max: 200 }
            }
          ]
        },
        {
          category: 'performance',
          displayName: '性能参数',
          description: '防火、保温、隔音等性能要求',
          parameters: [
            {
              id: 'fire_rating',
              name: 'fireRating',
              displayName: '耐火等级',
              type: 'select',
              defaultValue: 'A级',
              required: true,
              description: '防火窗的耐火等级',
              category: 'performance',
              options: ['A级', 'B级', 'C级']
            },
            {
              id: 'thermal_insulation',
              name: 'thermalInsulation',
              displayName: '保温性能',
              type: 'select',
              defaultValue: '标准',
              required: false,
              description: '保温隔热性能等级',
              category: 'performance',
              options: ['标准', '增强', '高性能']
            }
          ]
        },
        {
          category: 'material',
          displayName: '材质参数',
          description: '型材、密封材料等材质选择',
          parameters: [
            {
              id: 'profile_material',
              name: 'profileMaterial',
              displayName: '型材材质',
              type: 'select',
              defaultValue: '铝合金',
              required: true,
              description: '窗框型材的材质',
              category: 'material',
              options: ['铝合金', '钢材', '复合材料']
            },
            {
              id: 'sealant_type',
              name: 'sealantType',
              displayName: '密封胶类型',
              type: 'select',
              defaultValue: '防火密封胶',
              required: true,
              description: '密封胶条的类型',
              category: 'material',
              options: ['防火密封胶', '结构胶', '耐候胶']
            }
          ]
        }
      ],
      commonComponents: ['frame', 'seal'],
      requiredParameters: ['overallWidth', 'overallHeight', 'frameDepth', 'fireRating', 'profileMaterial']
    });

    // 防火窗扇构件模板
    this.templates.set('glass_assembly', {
      assemblyType: 'glass_assembly',
      name: '防火窗扇构件',
      description: '防火窗的扇框部分，包含扇框型材、防火玻璃、填充材料等',
      parameterGroups: [
        {
          category: 'dimensions',
          displayName: '尺寸参数',
          parameters: [
            {
              id: 'sash_width',
              name: 'sashWidth',
              displayName: '扇宽度',
              type: 'number',
              defaultValue: 600,
              required: true,
              description: '窗扇的宽度',
              unit: 'mm',
              category: 'dimensions',
              range: { min: 200, max: 1500 }
            },
            {
              id: 'sash_height',
              name: 'sashHeight',
              displayName: '扇高度',
              type: 'number',
              defaultValue: 1400,
              required: true,
              description: '窗扇的高度',
              unit: 'mm',
              category: 'dimensions',
              range: { min: 200, max: 2800 }
            }
          ]
        },
        {
          category: 'performance',
          displayName: '性能参数',
          parameters: [
            {
              id: 'glass_fire_rating',
              name: 'glassFireRating',
              displayName: '玻璃耐火等级',
              type: 'select',
              defaultValue: 'A级',
              required: true,
              description: '防火玻璃的耐火等级',
              category: 'performance',
              options: ['A级', 'B级', 'C级']
            }
          ]
        },
        {
          category: 'material',
          displayName: '材质参数',
          parameters: [
            {
              id: 'glass_type',
              name: 'glassType',
              displayName: '玻璃类型',
              type: 'select',
              defaultValue: '防火玻璃',
              required: true,
              description: '玻璃的类型',
              category: 'material',
              options: ['防火玻璃', '钢化玻璃', '夹胶玻璃']
            }
          ]
        }
      ],
      commonComponents: ['glass', 'frame'],
      requiredParameters: ['sashWidth', 'sashHeight', 'glassFireRating', 'glassType']
    });
  }

  /**
   * 获取构件类型的参数模板
   */
  getTemplate(assemblyType: AssemblyType): AssemblyParameterTemplate | null {
    return this.templates.get(assemblyType) || null;
  }

  /**
   * 获取所有可用的模板
   */
  getAllTemplates(): AssemblyParameterTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 基于构件类型推荐参数
   */
  recommendParameters(assemblyType: AssemblyType): ComponentParameter[] {
    const template = this.getTemplate(assemblyType);
    if (!template) {
      return [];
    }

    const parameters: ComponentParameter[] = [];
    template.parameterGroups.forEach(group => {
      parameters.push(...group.parameters);
    });

    return parameters;
  }

  /**
   * 检查参数完整性
   */
  checkParameterCompleteness(
    assemblyType: AssemblyType,
    currentParameters: ComponentParameter[],
    selectedComponents: Component[]
  ): ParameterCompletenessResult {
    const template = this.getTemplate(assemblyType);
    const result: ParameterCompletenessResult = {
      isComplete: true,
      missingParameters: [],
      suggestions: [],
      conflicts: []
    };

    if (!template) {
      return result;
    }

    // 检查必需参数
    const currentParamNames = new Set(currentParameters.map(p => p.name));
    const missingRequired = template.requiredParameters.filter(
      paramName => !currentParamNames.has(paramName)
    );

    if (missingRequired.length > 0) {
      result.isComplete = false;
      // 从模板中找到缺失的参数定义
      template.parameterGroups.forEach(group => {
        group.parameters.forEach(param => {
          if (missingRequired.includes(param.name)) {
            result.missingParameters.push(param);
          }
        });
      });
    }

    // 基于选中的组件推荐额外参数
    const componentSuggestions = this.suggestParametersFromComponents(
      selectedComponents,
      currentParameters
    );
    result.suggestions.push(...componentSuggestions);

    // 检查参数冲突
    result.conflicts = this.detectParameterConflicts(currentParameters);

    return result;
  }

  /**
   * 基于组件推荐参数
   */
  private suggestParametersFromComponents(
    components: Component[],
    currentParameters: ComponentParameter[]
  ): ComponentParameter[] {
    const suggestions: ComponentParameter[] = [];
    const currentParamNames = new Set(currentParameters.map(p => p.name));

    components.forEach(component => {
      component.parameters.forEach(param => {
        // 如果组件参数在构件级别还没有对应的参数，建议添加
        if (!currentParamNames.has(param.name) && param.required) {
          // 创建构件级别的参数建议
          const suggestion: ComponentParameter = {
            ...param,
            id: `suggested_${param.name}_${Date.now()}`,
            description: `基于组件"${component.name}"的参数"${param.displayName}"建议添加`
          };
          suggestions.push(suggestion);
        }
      });
    });

    return suggestions;
  }

  /**
   * 检测参数冲突
   */
  private detectParameterConflicts(parameters: ComponentParameter[]): ParameterConflict[] {
    const conflicts: ParameterConflict[] = [];
    const paramMap = new Map<string, ComponentParameter[]>();

    // 按名称分组参数
    parameters.forEach(param => {
      if (!paramMap.has(param.name)) {
        paramMap.set(param.name, []);
      }
      paramMap.get(param.name)!.push(param);
    });

    // 检查重复参数
    paramMap.forEach((params, name) => {
      if (params.length > 1) {
        conflicts.push({
          parameterName: name,
          conflictType: 'duplicate',
          description: `参数"${name}"存在重复定义`,
          suggestions: ['合并重复参数', '重命名其中一个参数']
        });
      }
    });

    return conflicts;
  }
}

// 导出单例实例
export const assemblyTemplateService = new AssemblyTemplateService();
