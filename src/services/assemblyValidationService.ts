/**
 * 构件验证服务
 * 
 * 验证构件的一致性、约束条件和数据完整性
 */

import type {
  Assembly,
  ComponentInstance,
  AssemblyParameter,
  AssemblyConstraint,
  ValidationResult,
  Component
} from '@/types/product-structure';

import { componentService } from './componentService';
import { assemblyParameterService } from './assemblyParameterService';

/**
 * 构件验证结果
 */
export interface AssemblyValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误列表 */
  errors: ValidationResult[];
  /** 警告列表 */
  warnings: ValidationResult[];
  /** 信息列表 */
  infos: ValidationResult[];
  /** 验证摘要 */
  summary: ValidationSummary;
}

/**
 * 验证摘要
 */
export interface ValidationSummary {
  /** 总验证项数 */
  totalChecks: number;
  /** 通过的验证项数 */
  passedChecks: number;
  /** 失败的验证项数 */
  failedChecks: number;
  /** 警告数 */
  warningCount: number;
  /** 验证覆盖率 */
  coverage: number;
}

/**
 * 数据一致性检查结果
 */
export interface DataConsistencyResult {
  /** 组件引用一致性 */
  componentReferenceConsistency: boolean;
  /** 参数定义一致性 */
  parameterDefinitionConsistency: boolean;
  /** 约束条件一致性 */
  constraintConsistency: boolean;
  /** 不一致的项目 */
  inconsistencies: DataInconsistency[];
}

/**
 * 数据不一致项
 */
export interface DataInconsistency {
  /** 不一致类型 */
  type: 'component_reference' | 'parameter_definition' | 'constraint_violation';
  /** 描述 */
  description: string;
  /** 影响的对象 */
  affectedObjects: string[];
  /** 建议修复方案 */
  suggestedFix?: string;
}

/**
 * 构件验证服务类
 */
export class AssemblyValidationService {

  /**
   * 验证构件
   */
  async validateAssembly(assembly: Assembly): Promise<AssemblyValidationResult> {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    const infos: ValidationResult[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    // 1. 基本信息验证
    const basicValidation = this.validateBasicInfo(assembly);
    totalChecks += basicValidation.totalChecks;
    passedChecks += basicValidation.passedChecks;
    errors.push(...basicValidation.errors);
    warnings.push(...basicValidation.warnings);

    // 2. 组件实例验证
    const instanceValidation = await this.validateComponentInstances(assembly.componentInstances);
    totalChecks += instanceValidation.totalChecks;
    passedChecks += instanceValidation.passedChecks;
    errors.push(...instanceValidation.errors);
    warnings.push(...instanceValidation.warnings);

    // 3. 参数验证
    const parameterValidation = this.validateAssemblyParameters(
      assembly.assemblyParameters,
      assembly.componentInstances
    );
    totalChecks += parameterValidation.totalChecks;
    passedChecks += parameterValidation.passedChecks;
    errors.push(...parameterValidation.errors);
    warnings.push(...parameterValidation.warnings);

    // 4. 约束验证
    const constraintValidation = this.validateAssemblyConstraints(
      assembly.assemblyConstraints,
      assembly.assemblyParameters,
      assembly.componentInstances
    );
    totalChecks += constraintValidation.totalChecks;
    passedChecks += constraintValidation.passedChecks;
    errors.push(...constraintValidation.errors);
    warnings.push(...constraintValidation.warnings);

    // 5. 数据一致性验证
    const consistencyValidation = await this.validateDataConsistency(assembly);
    totalChecks += consistencyValidation.totalChecks;
    passedChecks += consistencyValidation.passedChecks;
    errors.push(...consistencyValidation.errors);
    warnings.push(...consistencyValidation.warnings);

    const summary: ValidationSummary = {
      totalChecks,
      passedChecks,
      failedChecks: totalChecks - passedChecks,
      warningCount: warnings.length,
      coverage: totalChecks > 0 ? (passedChecks / totalChecks) * 100 : 0
    };

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      infos,
      summary
    };
  }

  /**
   * 验证基本信息
   */
  private validateBasicInfo(assembly: Assembly): {
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  } {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    // 检查必需字段
    totalChecks++;
    if (!assembly.name || assembly.name.trim() === '') {
      errors.push({
        type: 'error',
        message: '构件名称不能为空',
        field: 'name'
      });
    } else {
      passedChecks++;
    }

    totalChecks++;
    if (!assembly.code || assembly.code.trim() === '') {
      errors.push({
        type: 'error',
        message: '构件编码不能为空',
        field: 'code'
      });
    } else {
      passedChecks++;
    }

    // 检查编码格式
    totalChecks++;
    if (assembly.code && !/^[A-Z0-9_]+$/.test(assembly.code)) {
      warnings.push({
        type: 'warning',
        message: '构件编码建议只包含大写字母、数字和下划线',
        field: 'code'
      });
    } else {
      passedChecks++;
    }

    // 检查描述长度
    totalChecks++;
    if (assembly.description && assembly.description.length > 500) {
      warnings.push({
        type: 'warning',
        message: '构件描述过长，建议控制在500字符以内',
        field: 'description'
      });
    } else {
      passedChecks++;
    }

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 验证组件实例
   */
  private async validateComponentInstances(instances: ComponentInstance[]): Promise<{
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  }> {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    for (const instance of instances) {
      // 检查组件引用
      totalChecks++;
      try {
        const component = await componentService.getComponentById(instance.componentId);
        if (!component) {
          errors.push({
            type: 'error',
            message: `组件实例 "${instance.instanceName}" 引用的组件 ${instance.componentId} 不存在`,
            field: `componentInstances.${instance.id}.componentId`
          });
        } else {
          passedChecks++;

          // 验证参数
          const paramValidation = this.validateInstanceParameters(instance, component);
          totalChecks += paramValidation.totalChecks;
          passedChecks += paramValidation.passedChecks;
          errors.push(...paramValidation.errors);
          warnings.push(...paramValidation.warnings);
        }
      } catch (error) {
        errors.push({
          type: 'error',
          message: `验证组件实例 "${instance.instanceName}" 时发生错误: ${error}`,
          field: `componentInstances.${instance.id}`
        });
      }

      // 检查实例名称唯一性
      totalChecks++;
      const duplicateNames = instances.filter(i => i.instanceName === instance.instanceName);
      if (duplicateNames.length > 1) {
        errors.push({
          type: 'error',
          message: `组件实例名称 "${instance.instanceName}" 重复`,
          field: `componentInstances.${instance.id}.instanceName`
        });
      } else {
        passedChecks++;
      }

      // 检查数量
      totalChecks++;
      if (instance.quantityConfig.fixedQuantity && instance.quantityConfig.fixedQuantity <= 0) {
        errors.push({
          type: 'error',
          message: `组件实例 "${instance.instanceName}" 的数量必须大于0`,
          field: `componentInstances.${instance.id}.quantityConfig.fixedQuantity`
        });
      } else {
        passedChecks++;
      }
    }

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 验证实例参数
   */
  private validateInstanceParameters(instance: ComponentInstance, component: Component): {
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  } {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    // 检查必需参数
    component.parameters.forEach(param => {
      if (param.required) {
        totalChecks++;
        const value = instance.parameterConfig.parameterOverrides[param.name];
        if (value === undefined || value === null || value === '') {
          errors.push({
            type: 'error',
            message: `组件实例 "${instance.instanceName}" 缺少必需参数 "${param.displayName}"`,
            field: `componentInstances.${instance.id}.parameterConfig.parameterOverrides.${param.name}`
          });
        } else {
          passedChecks++;
        }
      }
    });

    // 检查参数类型和范围
    Object.entries(instance.parameterConfig.parameterOverrides).forEach(([paramName, value]) => {
      const param = component.parameters.find(p => p.name === paramName);
      if (param) {
        totalChecks++;
        const validation = assemblyParameterService.validateParameterValue(param as any, value);
        if (!validation.isValid) {
          errors.push({
            type: 'error',
            message: validation.errorMessage || `参数 "${param.displayName}" 值无效`,
            field: `componentInstances.${instance.id}.parameterConfig.parameterOverrides.${paramName}`
          });
        } else {
          passedChecks++;
        }
      }
    });

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 验证构件参数
   */
  private validateAssemblyParameters(
    parameters: AssemblyParameter[],
    instances: ComponentInstance[]
  ): {
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  } {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    // 检查参数名称唯一性
    const paramNames = parameters.map(p => p.name);
    const duplicateNames = paramNames.filter((name, index) => paramNames.indexOf(name) !== index);
    
    duplicateNames.forEach(name => {
      totalChecks++;
      errors.push({
        type: 'error',
        message: `构件参数名称 "${name}" 重复`,
        field: `assemblyParameters.${name}`
      });
    });
    passedChecks += paramNames.length - duplicateNames.length;

    // 检查循环依赖
    totalChecks++;
    const circularDeps = assemblyParameterService.checkCircularDependencies(parameters);
    if (circularDeps.length > 0) {
      circularDeps.forEach(dep => {
        errors.push({
          type: 'error',
          message: dep,
          field: 'assemblyParameters'
        });
      });
    } else {
      passedChecks++;
    }

    // 检查参数传播规则
    parameters.forEach(param => {
      param.propagationRules.forEach(rule => {
        totalChecks++;
        const targetInstance = instances.find(i => i.id === rule.targetInstanceId);
        if (!targetInstance) {
          errors.push({
            type: 'error',
            message: `参数 "${param.name}" 的传播规则引用了不存在的组件实例 ${rule.targetInstanceId}`,
            field: `assemblyParameters.${param.name}.propagationRules`
          });
        } else {
          passedChecks++;
        }
      });
    });

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 验证构件约束
   */
  private validateAssemblyConstraints(
    constraints: AssemblyConstraint[],
    parameters: AssemblyParameter[],
    instances: ComponentInstance[]
  ): {
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  } {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    constraints.forEach(constraint => {
      if (constraint.enabled) {
        totalChecks++;
        try {
          // 构建验证上下文
          const context: Record<string, any> = {};
          
          // 添加参数值
          parameters.forEach(param => {
            context[param.name] = assemblyParameterService.calculateParameterValue(param, context);
          });

          // 验证约束表达式
          const result = this.evaluateConstraint(constraint.expression, context);
          if (!result) {
            if (constraint.severity === 'error') {
              errors.push({
                type: 'error',
                message: constraint.errorMessage,
                field: `assemblyConstraints.${constraint.id}`
              });
            } else if (constraint.severity === 'warning') {
              warnings.push({
                type: 'warning',
                message: constraint.errorMessage,
                field: `assemblyConstraints.${constraint.id}`
              });
            }
          } else {
            passedChecks++;
          }
        } catch (error) {
          errors.push({
            type: 'error',
            message: `约束 "${constraint.name}" 表达式计算错误: ${error}`,
            field: `assemblyConstraints.${constraint.id}.expression`
          });
        }
      }
    });

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 验证数据一致性
   */
  private async validateDataConsistency(assembly: Assembly): Promise<{
    errors: ValidationResult[];
    warnings: ValidationResult[];
    totalChecks: number;
    passedChecks: number;
  }> {
    const errors: ValidationResult[] = [];
    const warnings: ValidationResult[] = [];
    let totalChecks = 0;
    let passedChecks = 0;

    // 检查组件引用一致性
    for (const instance of assembly.componentInstances) {
      totalChecks++;
      try {
        const component = await componentService.getComponentById(instance.componentId);
        if (component) {
          // 检查组件信息是否一致
          if (instance.componentCode !== component.code || instance.componentName !== component.name) {
            warnings.push({
              type: 'warning',
              message: `组件实例 "${instance.instanceName}" 的组件信息可能已过期，建议更新`,
              field: `componentInstances.${instance.id}`
            });
          } else {
            passedChecks++;
          }
        }
      } catch (error) {
        errors.push({
          type: 'error',
          message: `检查组件实例 "${instance.instanceName}" 一致性时发生错误: ${error}`,
          field: `componentInstances.${instance.id}`
        });
      }
    }

    return { errors, warnings, totalChecks, passedChecks };
  }

  /**
   * 计算约束表达式
   */
  private evaluateConstraint(expression: string, variables: Record<string, any>): boolean {
    try {
      let expr = expression;
      
      // 替换变量
      Object.entries(variables).forEach(([key, value]) => {
        expr = expr.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
      });
      
      // 注意：在生产环境中不应使用 eval，应使用专门的表达式解析库
      const result = Function(`"use strict"; return (${expr})`)();
      return Boolean(result);
    } catch (error) {
      throw new Error(`约束表达式计算错误: ${expression}`);
    }
  }

  /**
   * 快速验证（仅检查关键项）
   */
  async quickValidate(assembly: Assembly): Promise<boolean> {
    // 检查基本信息
    if (!assembly.name || !assembly.code) {
      return false;
    }

    // 检查组件实例引用
    for (const instance of assembly.componentInstances) {
      try {
        const component = await componentService.getComponentById(instance.componentId);
        if (!component) {
          return false;
        }
      } catch (error) {
        return false;
      }
    }

    return true;
  }
}

// 导出服务实例
export const assemblyValidationService = new AssemblyValidationService();
