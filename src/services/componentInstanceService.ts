/**
 * 组件实例服务
 * 
 * 管理构件中组件实例的生命周期，处理参数继承、约束验证等
 */

import type {
  ComponentInstance,
  Component,
  AssemblyParameter,
  ComponentInstanceParameterConfig,
  ParameterValidationResult,
  ParameterPropagationRule
} from '@/types/product-structure';

import { componentService } from './componentService';

/**
 * 组件实例创建配置
 */
export interface ComponentInstanceCreateConfig {
  /** 组件ID */
  componentId: string;
  /** 实例名称 */
  instanceName: string;
  /** 实例描述 */
  description?: string;
  /** 初始参数值 */
  initialParameters?: Record<string, any>;
  /** 数量 */
  quantity?: number;
  /** 位置 */
  position?: { x: number; y: number; z: number };
  /** 是否可选 */
  optional?: boolean;
}

/**
 * 参数继承结果
 */
export interface ParameterInheritanceResult {
  /** 继承的参数值 */
  inheritedValues: Record<string, any>;
  /** 覆盖的参数值 */
  overriddenValues: Record<string, any>;
  /** 最终参数值 */
  finalValues: Record<string, any>;
  /** 验证结果 */
  validationResults: Record<string, ParameterValidationResult>;
}

/**
 * 组件实例服务类
 */
export class ComponentInstanceService {
  
  /**
   * 创建组件实例
   */
  async createComponentInstance(
    config: ComponentInstanceCreateConfig,
    assemblyParameters: AssemblyParameter[] = []
  ): Promise<ComponentInstance> {
    // 获取组件定义
    const component = await componentService.getComponentById(config.componentId);
    if (!component) {
      throw new Error(`组件 ${config.componentId} 不存在`);
    }

    // 创建实例
    const instance: ComponentInstance = {
      id: `ci_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      componentId: config.componentId,
      componentCode: component.code,
      componentName: component.name,
      instanceName: config.instanceName,
      description: config.description,
      
      parameterConfig: this.createParameterConfig(
        component,
        config.initialParameters || {},
        assemblyParameters
      ),
      
      quantityConfig: {
        fixedQuantity: config.quantity || 1,
        minQuantity: 1,
        unit: '个'
      },
      
      positionConfig: config.position ? {
        position: config.position,
        constraints: []
      } : undefined,
      
      constraintConfig: {
        relationshipConstraints: [],
        parameterConstraints: [],
        geometryConstraints: []
      },
      
      optional: config.optional || false,
      alternatives: [],
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return instance;
  }

  /**
   * 更新组件实例参数
   */
  async updateInstanceParameters(
    instance: ComponentInstance,
    parameterUpdates: Record<string, any>,
    assemblyParameters: AssemblyParameter[] = []
  ): Promise<ComponentInstance> {
    // 获取组件定义
    const component = await componentService.getComponentById(instance.componentId);
    if (!component) {
      throw new Error(`组件 ${instance.componentId} 不存在`);
    }

    // 更新参数配置
    const updatedParameterConfig = {
      ...instance.parameterConfig,
      parameterOverrides: {
        ...instance.parameterConfig.parameterOverrides,
        ...parameterUpdates
      }
    };

    // 重新验证参数
    updatedParameterConfig.validationStatus = this.validateParameters(
      component,
      updatedParameterConfig.parameterOverrides,
      assemblyParameters
    );

    return {
      ...instance,
      parameterConfig: updatedParameterConfig,
      updatedAt: new Date().toISOString()
    };
  }

  /**
   * 计算参数继承
   */
  calculateParameterInheritance(
    component: Component,
    instanceOverrides: Record<string, any>,
    assemblyParameters: AssemblyParameter[]
  ): ParameterInheritanceResult {
    const inheritedValues: Record<string, any> = {};
    const overriddenValues: Record<string, any> = {};
    const finalValues: Record<string, any> = {};

    // 从组件获取默认参数值
    component.parameters.forEach(param => {
      inheritedValues[param.name] = param.defaultValue;
      finalValues[param.name] = param.defaultValue;
    });

    // 应用实例覆盖
    Object.entries(instanceOverrides).forEach(([key, value]) => {
      if (inheritedValues.hasOwnProperty(key)) {
        overriddenValues[key] = value;
        finalValues[key] = value;
      }
    });

    // 验证最终参数值
    const validationResults = this.validateParameters(
      component,
      finalValues,
      assemblyParameters
    );

    return {
      inheritedValues,
      overriddenValues,
      finalValues,
      validationResults
    };
  }

  /**
   * 验证参数值
   */
  validateParameters(
    component: Component,
    parameterValues: Record<string, any>,
    assemblyParameters: AssemblyParameter[]
  ): Record<string, ParameterValidationResult> {
    const results: Record<string, ParameterValidationResult> = {};

    component.parameters.forEach(param => {
      const value = parameterValues[param.name];
      const result: ParameterValidationResult = {
        isValid: true
      };

      // 必需参数检查
      if (param.required && (value === undefined || value === null || value === '')) {
        result.isValid = false;
        result.errorMessage = `参数 ${param.displayName} 是必需的`;
      }

      // 数值范围检查
      if (param.type === 'number' && value !== undefined) {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          result.isValid = false;
          result.errorMessage = `参数 ${param.displayName} 必须是数值`;
        } else {
          if (param.minValue !== undefined && numValue < param.minValue) {
            result.isValid = false;
            result.errorMessage = `参数 ${param.displayName} 不能小于 ${param.minValue}`;
          }
          if (param.maxValue !== undefined && numValue > param.maxValue) {
            result.isValid = false;
            result.errorMessage = `参数 ${param.displayName} 不能大于 ${param.maxValue}`;
          }
        }
      }

      // 选项检查
      if (param.type === 'select' && param.options && value !== undefined) {
        const validOptions = param.options.map(opt => opt.value);
        if (!validOptions.includes(value)) {
          result.isValid = false;
          result.errorMessage = `参数 ${param.displayName} 的值不在有效选项中`;
        }
      }

      results[param.name] = result;
    });

    return results;
  }

  /**
   * 应用参数传播规则
   */
  applyParameterPropagation(
    sourceInstance: ComponentInstance,
    targetInstances: ComponentInstance[],
    propagationRules: ParameterPropagationRule[]
  ): ComponentInstance[] {
    return targetInstances.map(targetInstance => {
      const applicableRules = propagationRules.filter(
        rule => rule.targetInstanceId === targetInstance.id
      );

      if (applicableRules.length === 0) {
        return targetInstance;
      }

      const updatedOverrides = { ...targetInstance.parameterConfig.parameterOverrides };

      applicableRules.forEach(rule => {
        const sourceValue = sourceInstance.parameterConfig.parameterOverrides[rule.targetParameterName] ||
                           sourceInstance.parameterConfig.parameterOverrides[rule.targetParameterName];

        if (sourceValue !== undefined) {
          switch (rule.propagationType) {
            case 'direct':
              updatedOverrides[rule.targetParameterName] = sourceValue;
              break;
            case 'formula':
              if (rule.expression) {
                try {
                  // 简单的公式计算（实际项目中应使用更安全的表达式解析器）
                  const calculatedValue = this.evaluateFormula(rule.expression, { value: sourceValue });
                  updatedOverrides[rule.targetParameterName] = calculatedValue;
                } catch (error) {
                  console.warn(`参数传播公式计算失败: ${rule.expression}`, error);
                }
              }
              break;
            case 'mapping':
              if (rule.mapping && rule.mapping[sourceValue]) {
                updatedOverrides[rule.targetParameterName] = rule.mapping[sourceValue];
              }
              break;
          }
        }
      });

      return {
        ...targetInstance,
        parameterConfig: {
          ...targetInstance.parameterConfig,
          parameterOverrides: updatedOverrides
        },
        updatedAt: new Date().toISOString()
      };
    });
  }

  /**
   * 创建参数配置
   */
  private createParameterConfig(
    component: Component,
    initialParameters: Record<string, any>,
    assemblyParameters: AssemblyParameter[]
  ): ComponentInstanceParameterConfig {
    const validationStatus = this.validateParameters(
      component,
      initialParameters,
      assemblyParameters
    );

    return {
      parameterOverrides: initialParameters,
      parameterBindings: {},
      parameterFormulas: {},
      validationStatus
    };
  }

  /**
   * 简单的公式计算（示例实现）
   */
  private evaluateFormula(formula: string, variables: Record<string, any>): any {
    // 这里应该使用更安全的表达式解析器
    // 当前只是一个简单的示例
    let expression = formula;
    Object.entries(variables).forEach(([key, value]) => {
      expression = expression.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
    });
    
    try {
      // 注意：在生产环境中不应使用 eval，应使用专门的表达式解析库
      return Function(`"use strict"; return (${expression})`)();
    } catch (error) {
      throw new Error(`公式计算错误: ${formula}`);
    }
  }
}

// 导出服务实例
export const componentInstanceService = new ComponentInstanceService();
