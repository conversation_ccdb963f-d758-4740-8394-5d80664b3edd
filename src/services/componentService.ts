/**
 * 组件管理服务
 * 
 * 提供组件的CRUD操作、搜索筛选、批量操作等功能
 */

import type {
  Component,
  ComponentParameter,
  ComponentConstraint,
  ProcessRequirement,
  ComponentType,
  ParameterType,
  ConstraintType,
  SeverityLevel
} from '@/types/product-structure';

/**
 * 组件筛选条件
 */
export interface ComponentFilters {
  /** 搜索关键词 */
  search?: string;
  /** 组件类型 */
  componentType?: ComponentType[];
  /** 物料分类ID */
  materialCategoryId?: string[];
  /** 组件状态 */
  status?: ('draft' | 'active' | 'deprecated' | 'archived')[];
  /** 标签 */
  tags?: string[];
  /** 是否可重用 */
  reusable?: boolean;
  /** 创建时间范围 */
  createdDateRange?: [string, string];
  /** 更新时间范围 */
  updatedDateRange?: [string, string];
}

/**
 * 组件排序选项
 */
export interface ComponentSortOptions {
  /** 排序字段 */
  field: 'name' | 'code' | 'componentType' | 'createdAt' | 'updatedAt';
  /** 排序方向 */
  direction: 'asc' | 'desc';
}

/**
 * 批量操作类型
 */
export type BatchOperationType = 'delete' | 'updateStatus' | 'addTags' | 'removeTags' | 'export';

/**
 * 批量操作参数
 */
export interface BatchOperationParams {
  /** 操作类型 */
  type: BatchOperationType;
  /** 组件ID列表 */
  componentIds: string[];
  /** 操作参数 */
  params?: {
    status?: 'draft' | 'active' | 'deprecated' | 'archived';
    tags?: string[];
  };
}

/**
 * 组件统计信息
 */
export interface ComponentStatistics {
  /** 总数量 */
  total: number;
  /** 按类型分组统计 */
  byType: Record<ComponentType, number>;
  /** 按状态分组统计 */
  byStatus: Record<string, number>;
  /** 最近创建数量 */
  recentlyCreated: number;
  /** 最近更新数量 */
  recentlyUpdated: number;
}

/**
 * 组件管理服务类
 */
export class ComponentService {
  private components: Component[] = [];
  private nextId = 1;
  private dataLoaded = false;

  constructor() {
    // 不在构造函数中加载数据，而是在需要时异步加载
  }

  /**
   * 获取组件列表
   */
  async getComponents(
    filters?: ComponentFilters,
    sort?: ComponentSortOptions,
    pagination?: { page: number; pageSize: number }
  ): Promise<{
    components: Component[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    await this.ensureDataLoaded();
    let filteredComponents = [...this.components];

    // 应用筛选条件
    if (filters) {
      filteredComponents = this.applyFilters(filteredComponents, filters);
    }

    // 应用排序
    if (sort) {
      filteredComponents = this.applySorting(filteredComponents, sort);
    }

    const total = filteredComponents.length;

    // 应用分页
    if (pagination) {
      const startIndex = (pagination.page - 1) * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      filteredComponents = filteredComponents.slice(startIndex, endIndex);
    }

    return {
      components: filteredComponents,
      total,
      page: pagination?.page || 1,
      pageSize: pagination?.pageSize || total
    };
  }

  /**
   * 根据ID获取组件
   */
  async getComponentById(id: string): Promise<Component | null> {
    await this.ensureDataLoaded();
    return this.components.find(c => c.id === id) || null;
  }

  /**
   * 创建组件
   */
  async createComponent(componentData: Partial<Component>): Promise<Component> {
    const now = new Date().toISOString();
    const component: Component = {
      id: `component-${this.nextId++}`,
      code: componentData.code || `COMP${String(this.nextId).padStart(3, '0')}`,
      name: componentData.name || '新组件',
      description: componentData.description || '',
      version: 1,
      status: componentData.status || 'draft',
      createdAt: now,
      updatedAt: now,
      createdBy: 'current-user',
      updatedBy: 'current-user',
      componentType: componentData.componentType || 'other',
      materialCategoryId: componentData.materialCategoryId || '',
      materialCategoryName: componentData.materialCategoryName || '',
      materialCategoryCode: componentData.materialCategoryCode || '',
      parameters: componentData.parameters || [],
      quantityFormula: componentData.quantityFormula || '1',
      costFormula: componentData.costFormula,
      constraints: componentData.constraints || [],
      processRequirements: componentData.processRequirements || [],
      properties: componentData.properties || {},
      tags: componentData.tags || [],
      reusable: componentData.reusable !== undefined ? componentData.reusable : true
    };

    this.components.push(component);
    return component;
  }

  /**
   * 更新组件
   */
  async updateComponent(id: string, updates: Partial<Component>): Promise<Component> {
    const index = this.components.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error(`组件 ${id} 不存在`);
    }

    const component = this.components[index];
    const updatedComponent: Component = {
      ...component,
      ...updates,
      id: component.id, // 确保ID不被修改
      updatedAt: new Date().toISOString(),
      updatedBy: 'current-user'
    };

    this.components[index] = updatedComponent;
    return updatedComponent;
  }

  /**
   * 删除组件
   */
  async deleteComponent(id: string): Promise<void> {
    const index = this.components.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error(`组件 ${id} 不存在`);
    }

    this.components.splice(index, 1);
  }

  /**
   * 批量操作
   */
  async batchOperation(operation: BatchOperationParams): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    const result = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const componentId of operation.componentIds) {
      try {
        switch (operation.type) {
          case 'delete':
            await this.deleteComponent(componentId);
            break;
          case 'updateStatus':
            if (operation.params?.status) {
              await this.updateComponent(componentId, { status: operation.params.status });
            }
            break;
          case 'addTags':
            if (operation.params?.tags) {
              const component = await this.getComponentById(componentId);
              if (component) {
                const newTags = [...new Set([...component.tags, ...operation.params.tags])];
                await this.updateComponent(componentId, { tags: newTags });
              }
            }
            break;
          case 'removeTags':
            if (operation.params?.tags) {
              const component = await this.getComponentById(componentId);
              if (component) {
                const newTags = component.tags.filter(tag => !operation.params!.tags!.includes(tag));
                await this.updateComponent(componentId, { tags: newTags });
              }
            }
            break;
        }
        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push(`组件 ${componentId}: ${error.message}`);
      }
    }

    return result;
  }

  /**
   * 获取组件统计信息
   */
  async getStatistics(): Promise<ComponentStatistics> {
    const total = this.components.length;
    const byType: Record<ComponentType, number> = {
      frame: 0,
      glass: 0,
      hardware: 0,
      seal: 0,
      other: 0
    };
    const byStatus: Record<string, number> = {};

    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    let recentlyCreated = 0;
    let recentlyUpdated = 0;

    this.components.forEach(component => {
      // 按类型统计
      byType[component.componentType]++;

      // 按状态统计
      byStatus[component.status] = (byStatus[component.status] || 0) + 1;

      // 最近创建/更新统计
      const createdDate = new Date(component.createdAt);
      const updatedDate = new Date(component.updatedAt);

      if (createdDate >= sevenDaysAgo) {
        recentlyCreated++;
      }
      if (updatedDate >= sevenDaysAgo && updatedDate > createdDate) {
        recentlyUpdated++;
      }
    });

    return {
      total,
      byType,
      byStatus,
      recentlyCreated,
      recentlyUpdated
    };
  }

  /**
   * 复制组件
   */
  async duplicateComponent(id: string, newName?: string): Promise<Component> {
    const originalComponent = await this.getComponentById(id);
    if (!originalComponent) {
      throw new Error(`组件 ${id} 不存在`);
    }

    const duplicatedComponent = {
      ...originalComponent,
      name: newName || `${originalComponent.name} (副本)`,
      code: `${originalComponent.code}_COPY`,
      status: 'draft' as const
    };

    delete (duplicatedComponent as any).id; // 删除ID，让createComponent生成新的ID
    return this.createComponent(duplicatedComponent);
  }

  /**
   * 应用筛选条件
   */
  private applyFilters(components: Component[], filters: ComponentFilters): Component[] {
    return components.filter(component => {
      // 搜索关键词
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          component.name.toLowerCase().includes(searchLower) ||
          component.code.toLowerCase().includes(searchLower) ||
          component.description?.toLowerCase().includes(searchLower) ||
          component.tags.some(tag => tag.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // 组件类型
      if (filters.componentType && filters.componentType.length > 0) {
        if (!filters.componentType.includes(component.componentType)) return false;
      }

      // 物料分类
      if (filters.materialCategoryId && filters.materialCategoryId.length > 0) {
        if (!filters.materialCategoryId.includes(component.materialCategoryId)) return false;
      }

      // 状态
      if (filters.status && filters.status.length > 0) {
        if (!filters.status.includes(component.status)) return false;
      }

      // 标签
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => component.tags.includes(tag));
        if (!hasMatchingTag) return false;
      }

      // 是否可重用
      if (filters.reusable !== undefined) {
        if (component.reusable !== filters.reusable) return false;
      }

      // 创建时间范围
      if (filters.createdDateRange) {
        const createdDate = new Date(component.createdAt);
        const startDate = new Date(filters.createdDateRange[0]);
        const endDate = new Date(filters.createdDateRange[1]);
        if (createdDate < startDate || createdDate > endDate) return false;
      }

      // 更新时间范围
      if (filters.updatedDateRange) {
        const updatedDate = new Date(component.updatedAt);
        const startDate = new Date(filters.updatedDateRange[0]);
        const endDate = new Date(filters.updatedDateRange[1]);
        if (updatedDate < startDate || updatedDate > endDate) return false;
      }

      return true;
    });
  }

  /**
   * 应用排序
   */
  private applySorting(components: Component[], sort: ComponentSortOptions): Component[] {
    return components.sort((a, b) => {
      let aValue: any = a[sort.field];
      let bValue: any = b[sort.field];

      // 处理日期字段
      if (sort.field === 'createdAt' || sort.field === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // 处理字符串字段
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      let comparison = 0;
      if (aValue < bValue) {
        comparison = -1;
      } else if (aValue > bValue) {
        comparison = 1;
      }

      return sort.direction === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * 确保数据已加载
   */
  private async ensureDataLoaded(): Promise<void> {
    if (!this.dataLoaded) {
      await this.loadMockData();
    }
  }

  /**
   * 加载Mock数据
   */
  private async loadMockData(): Promise<void> {
    try {
      const response = await fetch('/mock/product/components.json');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      this.components = data.components || [];
      this.nextId = Math.max(...this.components.map(c => parseInt(c.id.replace('comp_', '')))) + 1;
      this.dataLoaded = true;
    } catch (error) {
      console.error('加载组件数据失败:', error);
      this.components = [];
      this.dataLoaded = true; // 即使失败也标记为已加载，避免重复尝试
    }
  }
}

// 导出单例实例
export const componentService = new ComponentService();
