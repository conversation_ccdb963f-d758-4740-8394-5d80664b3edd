/**
 * 参数化设计和约束管理集成服务
 * 
 * 整合参数验证引擎和约束求解系统，提供统一的参数化设计和约束管理接口
 */

import { ParameterValidationEngine, type ParameterValidationResult, type ParameterDependency } from './parameterValidationEngine';
import { ConstraintSolver, type ConstraintSolverResult, type ConstraintConflict, type ConstraintSuggestion } from './constraintSolver';
import type {
  ComponentParameter,
  ComponentConstraint,
  ValidationResult,
  ValidationError,
  Component,
  Assembly,
  ProductStructure
} from '@/types/product-structure';

/**
 * 完整的参数约束验证结果
 */
export interface ParameterConstraintValidationResult {
  /** 参数验证结果 */
  parameterValidation: ValidationResult;
  /** 约束验证结果 */
  constraintValidation: ValidationResult;
  /** 约束求解结果 */
  constraintSolverResult: ConstraintSolverResult;
  /** 整体是否有效 */
  isValid: boolean;
  /** 建议的参数值 */
  suggestedValues?: Record<string, any>;
  /** 修复建议 */
  fixSuggestions: ConstraintSuggestion[];
}

/**
 * 参数约束配置
 */
export interface ParameterConstraintConfig {
  /** 参数依赖关系 */
  dependencies?: Record<string, ParameterDependency[]>;
  /** 变量范围限制 */
  variableRanges?: Record<string, { min?: number; max?: number }>;
  /** 固定变量（不允许自动调整） */
  fixedVariables?: string[];
  /** 是否启用自动修复 */
  enableAutoFix?: boolean;
}

/**
 * 参数化设计和约束管理服务
 */
export class ParameterConstraintService {
  private parameterEngine: ParameterValidationEngine;
  private constraintSolver: ConstraintSolver;

  constructor() {
    this.parameterEngine = new ParameterValidationEngine();
    this.constraintSolver = new ConstraintSolver();
  }

  /**
   * 配置服务
   */
  configure(config: ParameterConstraintConfig): void {
    if (config.dependencies) {
      this.parameterEngine.setDependencies(config.dependencies);
    }
    
    if (config.variableRanges) {
      this.constraintSolver.setVariableRanges(config.variableRanges);
    }
  }

  /**
   * 验证组件参数和约束
   */
  async validateComponent(
    component: Component,
    parameterValues: Record<string, any>,
    config?: ParameterConstraintConfig
  ): Promise<ParameterConstraintValidationResult> {
    if (config) {
      this.configure(config);
    }

    // 1. 验证参数
    const parameterValidation = this.parameterEngine.validateParameters(
      component.parameters,
      parameterValues
    );

    // 2. 验证约束
    const constraintValidation = this.constraintSolver.validateConstraints(
      component.constraints,
      parameterValidation.validatedValues || parameterValues
    );

    // 3. 约束求解
    const constraintSolverResult = this.constraintSolver.solveConstraints(
      component.constraints,
      parameterValidation.validatedValues || parameterValues,
      config?.fixedVariables || []
    );

    // 4. 生成综合结果
    const result: ParameterConstraintValidationResult = {
      parameterValidation,
      constraintValidation,
      constraintSolverResult,
      isValid: parameterValidation.isValid && constraintValidation.isValid && constraintSolverResult.isSatisfied,
      suggestedValues: constraintSolverResult.solvedValues,
      fixSuggestions: constraintSolverResult.suggestions
    };

    return result;
  }

  /**
   * 验证构件参数和约束
   */
  async validateAssembly(
    assembly: Assembly,
    parameterValues: Record<string, any>,
    config?: ParameterConstraintConfig
  ): Promise<ParameterConstraintValidationResult> {
    if (config) {
      this.configure(config);
    }

    // 收集所有参数和约束
    const allParameters: ComponentParameter[] = [...assembly.assemblyParameters];
    const allConstraints: ComponentConstraint[] = [...assembly.assemblyConstraints];

    // 添加组件实例的参数和约束
    assembly.componentInstances.forEach(instance => {
      // 这里需要从组件定义中获取参数和约束
      // 实际实现中需要查询组件服务
    });

    // 验证参数
    const parameterValidation = this.parameterEngine.validateParameters(
      allParameters,
      parameterValues
    );

    // 验证约束
    const constraintValidation = this.constraintSolver.validateConstraints(
      allConstraints,
      parameterValidation.validatedValues || parameterValues
    );

    // 约束求解
    const constraintSolverResult = this.constraintSolver.solveConstraints(
      allConstraints,
      parameterValidation.validatedValues || parameterValues,
      config?.fixedVariables || []
    );

    const result: ParameterConstraintValidationResult = {
      parameterValidation,
      constraintValidation,
      constraintSolverResult,
      isValid: parameterValidation.isValid && constraintValidation.isValid && constraintSolverResult.isSatisfied,
      suggestedValues: constraintSolverResult.solvedValues,
      fixSuggestions: constraintSolverResult.suggestions
    };

    return result;
  }

  /**
   * 验证产品结构参数和约束
   */
  async validateProductStructure(
    productStructure: ProductStructure,
    parameterValues: Record<string, any>,
    config?: ParameterConstraintConfig
  ): Promise<ParameterConstraintValidationResult> {
    if (config) {
      this.configure(config);
    }

    // 收集产品级参数和约束
    const allParameters: ComponentParameter[] = [...productStructure.productParameters];
    const allConstraints: ComponentConstraint[] = [...productStructure.productConstraints];

    // 递归收集所有子组件和构件的参数约束
    // 这里需要实现递归遍历逻辑

    // 验证参数
    const parameterValidation = this.parameterEngine.validateParameters(
      allParameters,
      parameterValues
    );

    // 验证约束
    const constraintValidation = this.constraintSolver.validateConstraints(
      allConstraints,
      parameterValidation.validatedValues || parameterValues
    );

    // 约束求解
    const constraintSolverResult = this.constraintSolver.solveConstraints(
      allConstraints,
      parameterValidation.validatedValues || parameterValues,
      config?.fixedVariables || []
    );

    const result: ParameterConstraintValidationResult = {
      parameterValidation,
      constraintValidation,
      constraintSolverResult,
      isValid: parameterValidation.isValid && constraintValidation.isValid && constraintSolverResult.isSatisfied,
      suggestedValues: constraintSolverResult.solvedValues,
      fixSuggestions: constraintSolverResult.suggestions
    };

    return result;
  }

  /**
   * 检测参数依赖关系
   */
  async detectParameterDependencies(
    parameters: ComponentParameter[],
    constraints: ComponentConstraint[]
  ): Promise<Record<string, ParameterDependency[]>> {
    const dependencies: Record<string, ParameterDependency[]> = {};

    // 从约束中分析参数依赖关系
    constraints.forEach(constraint => {
      try {
        const parsed = this.constraintSolver.parseConstraint(constraint);
        const variables = parsed.variables;

        // 简单的依赖关系检测
        // 实际应用中需要更复杂的分析
        if (variables.length > 1) {
          variables.forEach((variable, index) => {
            const otherVars = variables.filter((_, i) => i !== index);
            
            if (!dependencies[variable]) {
              dependencies[variable] = [];
            }

            otherVars.forEach(otherVar => {
              dependencies[variable].push({
                dependsOn: otherVar,
                condition: `${otherVar} != null`,
                action: 'require'
              });
            });
          });
        }
      } catch (error) {
        console.warn(`分析约束依赖关系失败: ${constraint.name}`, error);
      }
    });

    return dependencies;
  }

  /**
   * 生成参数配置建议
   */
  async generateParameterSuggestions(
    parameters: ComponentParameter[],
    constraints: ComponentConstraint[],
    currentValues: Record<string, any>
  ): Promise<{
    parameterSuggestions: Record<string, any>;
    constraintSuggestions: ConstraintSuggestion[];
    reasoning: string[];
  }> {
    const suggestions: Record<string, any> = {};
    const reasoning: string[] = [];

    // 1. 为缺失的必填参数提供默认值
    parameters.forEach(param => {
      if (param.required && !(param.name in currentValues)) {
        if (param.defaultValue !== undefined) {
          suggestions[param.name] = param.defaultValue;
          reasoning.push(`为必填参数 ${param.displayName} 设置默认值: ${param.defaultValue}`);
        } else if (param.type === 'number' && param.minValue !== undefined) {
          suggestions[param.name] = param.minValue;
          reasoning.push(`为必填参数 ${param.displayName} 设置最小值: ${param.minValue}`);
        }
      }
    });

    // 2. 基于约束生成建议
    const mergedValues = { ...currentValues, ...suggestions };
    const constraintValidation = this.constraintSolver.validateConstraints(constraints, mergedValues);

    // 如果存在约束违反，尝试调整参数
    if (!constraintValidation.isValid) {
      for (const error of constraintValidation.errors) {
        if (error.type === 'constraint_violation') {
          // 尝试找到相关的约束并调整参数
          const constraint = constraints.find(c => c.id === error.id.replace('constraint_', ''));
          if (constraint) {
            const adjustedValues = this.adjustParametersForConstraint(
              constraint,
              parameters,
              mergedValues
            );
            Object.assign(suggestions, adjustedValues);
            reasoning.push(`为满足约束 "${constraint.name}" 调整了参数值`);
          }
        }
      }
    }

    const constraintSolverResult = this.constraintSolver.solveConstraints(
      constraints,
      { ...currentValues, ...suggestions }
    );

    if (constraintSolverResult.solvedValues) {
      // 只采用那些有实际改进的值
      Object.entries(constraintSolverResult.solvedValues).forEach(([key, value]) => {
        if (value !== mergedValues[key]) {
          suggestions[key] = value;
        }
      });
      if (Object.keys(constraintSolverResult.solvedValues).length > 0) {
        reasoning.push('基于约束求解调整了参数值');
      }
    }

    return {
      parameterSuggestions: suggestions,
      constraintSuggestions: constraintSolverResult.suggestions,
      reasoning
    };
  }

  /**
   * 优化参数配置
   */
  async optimizeParameterConfiguration(
    parameters: ComponentParameter[],
    constraints: ComponentConstraint[],
    currentValues: Record<string, any>,
    objectives: { parameter: string; target: 'minimize' | 'maximize' }[]
  ): Promise<{
    optimizedValues: Record<string, any>;
    improvementScore: number;
    optimizationSteps: string[];
  }> {
    const optimizedValues = { ...currentValues };
    const optimizationSteps: string[] = [];
    let improvementScore = 0;

    // 简化的优化实现
    // 实际应用中应该使用更复杂的优化算法

    for (const objective of objectives) {
      const param = parameters.find(p => p.name === objective.parameter);
      if (!param || param.type !== 'number') continue;

      const currentValue = currentValues[objective.parameter];
      if (typeof currentValue !== 'number') continue;

      // 尝试不同的值来优化目标
      const testValues = [];
      if (param.minValue !== undefined && param.maxValue !== undefined) {
        const range = param.maxValue - param.minValue;
        for (let i = 0; i <= 10; i++) {
          testValues.push(param.minValue + (range * i / 10));
        }
      } else {
        // 在当前值附近搜索
        for (let factor = 0.5; factor <= 2.0; factor += 0.1) {
          testValues.push(currentValue * factor);
        }
      }

      let bestValue = currentValue;
      let bestScore = 0;

      for (const testValue of testValues) {
        const testConfig = { ...optimizedValues, [objective.parameter]: testValue };
        
        // 验证约束
        const validation = this.constraintSolver.validateConstraints(constraints, testConfig);
        if (!validation.isValid) continue;

        // 计算目标函数值
        const score = objective.target === 'minimize' ? -testValue : testValue;
        if (score > bestScore) {
          bestValue = testValue;
          bestScore = score;
        }
      }

      if (bestValue !== currentValue) {
        optimizedValues[objective.parameter] = bestValue;
        optimizationSteps.push(
          `优化参数 ${param.displayName}: ${currentValue} → ${bestValue} (${objective.target})`
        );
        improvementScore += Math.abs(bestValue - currentValue) / Math.abs(currentValue);
      }
    }

    return {
      optimizedValues,
      improvementScore,
      optimizationSteps
    };
  }

  /**
   * 获取参数验证引擎实例
   */
  getParameterEngine(): ParameterValidationEngine {
    return this.parameterEngine;
  }

  /**
   * 获取约束求解器实例
   */
  getConstraintSolver(): ConstraintSolver {
    return this.constraintSolver;
  }

  /**
   * 为满足约束调整参数
   */
  private adjustParametersForConstraint(
    constraint: ComponentConstraint,
    parameters: ComponentParameter[],
    currentValues: Record<string, any>
  ): Record<string, any> {
    const adjustments: Record<string, any> = {};

    try {
      const parsed = this.constraintSolver.parseConstraint(constraint);
      const variables = parsed.variables;

      // 简单的调整策略：对于数值参数，尝试小幅调整
      for (const variable of variables) {
        const param = parameters.find(p => p.name === variable);
        const currentValue = currentValues[variable];

        if (param && param.type === 'number' && typeof currentValue === 'number') {
          // 尝试不同的调整因子
          const adjustmentFactors = [1.1, 0.9, 1.2, 0.8, 1.5, 0.7];

          for (const factor of adjustmentFactors) {
            const testValue = currentValue * factor;

            // 检查是否在参数范围内
            if (param.minValue !== undefined && testValue < param.minValue) continue;
            if (param.maxValue !== undefined && testValue > param.maxValue) continue;

            // 测试这个值是否能满足约束
            const testValues = { ...currentValues, [variable]: testValue };
            try {
              const result = this.constraintSolver['evaluateConstraint'](parsed, testValues);
              if (result) {
                adjustments[variable] = testValue;
                break;
              }
            } catch (error) {
              // 继续尝试下一个因子
            }
          }
        }
      }
    } catch (error) {
      console.warn('调整参数时发生错误:', error);
    }

    return adjustments;
  }
}

// 导出单例实例
export const parameterConstraintService = new ParameterConstraintService();
