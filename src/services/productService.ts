import type {
  Component,
  Assembly,
  ProductStructure,
  ComponentFilters,
  AssemblyFilters,
  ProductStructureFilters,
  ValidationResult,
  BOMItem,
  StructureValidationResult,
  PaginationOptions,
  PaginatedResponse
} from '@/types/product-structure';

import type {
  ProductConfiguration
} from '@/types/product';

// 临时类型定义，直到找到正确的类型定义位置
interface Product {
  id: string;
  code: string;
  name: string;
  description?: string;
  productStructureId: string;
  productStructureCode: string;
  productStructureName: string;
  productStructureVersion: number;
  category: string;
  subCategory?: string;
  lifecycle: string;
  componentMaterialMap: any[];
  defaultParameters: Record<string, any>;
  status: string;
  tags: string[];
  documents: any[];
  statistics: any;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

interface QuoteBOM {
  id: string;
  code: string;
  name: string;
  productId: string;
  productCode: string;
  productName: string;
  productVersion: number;
  version: number;
  status: string;
  items: any[];
  configurationSnapshot: any;
  costSummary: any;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

interface ProductionBOM {
  id: string;
  code: string;
  name: string;
  productId: string;
  productCode: string;
  productName: string;
  version: number;
  status: string;
  items: any[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

interface ProductFilters {
  productStructureId?: string;
  category?: string;
  lifecycle?: string;
  status?: string;
  search?: string;
  tags?: string[];
}

interface QuoteBOMFilters {
  productId?: string;
  status?: string;
  search?: string;
}

interface ProductionBOMFilters {
  productId?: string;
  status?: string;
  search?: string;
}

// Mock数据加载器
class MockDataLoader {
  private static cache = new Map<string, any>();

  static async loadMockData<T>(path: string): Promise<T> {
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    try {
      const response = await fetch(`/mock/product/${path}`);
      if (!response.ok) {
        throw new Error(`Failed to load mock data: ${path}`);
      }
      const data = await response.json();
      this.cache.set(path, data);
      return data;
    } catch (error) {
      console.error(`Error loading mock data from ${path}:`, error);
      throw error;
    }
  }

  static clearCache() {
    this.cache.clear();
  }
}

// 组件服务
export class ComponentService {
  async getComponents(filters?: ComponentFilters): Promise<Component[]> {
    const data = await MockDataLoader.loadMockData<{ components: Component[] }>('components.json');
    let components = data.components;

    if (filters) {
      components = components.filter(component => {
        if (filters.componentType && filters.componentType.length > 0 && !filters.componentType.includes(component.componentType)) {
          return false;
        }
        if (filters.materialCategoryId && filters.materialCategoryId.length > 0 && !filters.materialCategoryId.includes(component.materialCategoryId)) {
          return false;
        }
        if (filters.status && filters.status.length > 0 && !filters.status.includes(component.status)) {
          return false;
        }
        if (filters.keyword) {
          const searchLower = filters.keyword.toLowerCase();
          const matchesKeyword = (
            component.name.toLowerCase().includes(searchLower) ||
            component.code.toLowerCase().includes(searchLower) ||
            component.description?.toLowerCase().includes(searchLower)
          );
          if (!matchesKeyword) {
            return false;
          }
        }
        if (filters.tags && filters.tags.length > 0) {
          const componentTags = component.properties?.tags || [];
          const hasMatchingTag = filters.tags.some(tag => componentTags.includes(tag));
          if (!hasMatchingTag) {
            return false;
          }
        }
        return true;
      });
    }

    return components;
  }

  async getComponentsPaginated(filters?: ComponentFilters, pagination?: PaginationOptions): Promise<PaginatedResponse<Component>> {
    const allComponents = await this.getComponents(filters);

    const page = pagination?.page || 1;
    const pageSize = pagination?.pageSize || 10;
    const sortBy = pagination?.sortBy || 'name';
    const sortOrder = pagination?.sortOrder || 'asc';

    // 排序
    const sortedComponents = [...allComponents].sort((a, b) => {
      const aValue = (a as any)[sortBy];
      const bValue = (b as any)[sortBy];

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const items = sortedComponents.slice(startIndex, endIndex);

    const total = sortedComponents.length;
    const totalPages = Math.ceil(total / pageSize);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1
    };
  }

  async getComponentById(id: string): Promise<Component | null> {
    const components = await this.getComponents();
    return components.find(c => c.id === id) || null;
  }

  async createComponent(component: Partial<Component>): Promise<Component> {
    const newComponent: Component = {
      id: `comp_${Date.now()}`,
      code: component.code || '',
      name: component.name || '',
      description: component.description,
      componentType: component.componentType || 'other',
      materialCategoryId: component.materialCategoryId || '',
      materialCategoryName: component.materialCategoryName || '',
      materialCategoryCode: component.materialCategoryCode || '',
      parameters: component.parameters || [],
      quantityFormula: component.quantityFormula || '1',
      costFormula: component.costFormula,
      constraints: component.constraints || [],
      processRequirements: component.processRequirements || [],
      properties: component.properties || {},
      tags: component.tags || [],
      reusable: component.reusable || true,
      status: component.status || 'draft',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    // 在实际应用中，这里会调用API保存到后端
    console.log('Creating component:', newComponent);
    return newComponent;
  }

  async updateComponent(id: string, updates: Partial<Component>): Promise<Component> {
    const component = await this.getComponentById(id);
    if (!component) {
      throw new Error(`Component with id ${id} not found`);
    }

    const updatedComponent: Component = {
      ...component,
      ...updates,
      id: component.id, // 确保ID不被覆盖
      version: component.version + 1,
      updatedAt: new Date().toISOString(),
      updatedBy: 'current_user'
    };

    console.log('Updating component:', updatedComponent);
    return updatedComponent;
  }

  async deleteComponent(id: string): Promise<void> {
    console.log('Deleting component:', id);
    // 在实际应用中，这里会调用API删除
  }

  async validateParameters(componentId: string, values: Record<string, any>): Promise<ValidationResult> {
    const component = await this.getComponentById(componentId);
    if (!component) {
      throw new Error(`Component with id ${componentId} not found`);
    }

    const errors: any[] = [];
    const warnings: any[] = [];

    // 验证约束条件
    for (const constraint of component.constraints) {
      try {
        // 简单的表达式验证（实际应用中需要更复杂的表达式解析器）
        const isValid = this.evaluateConstraint(constraint.expression, values);
        if (!isValid) {
          const error = {
            id: constraint.id,
            type: constraint.type,
            message: constraint.errorMessage,
            field: constraint.expression,
            severity: constraint.severity,
            context: {
              componentId,
              constraintId: constraint.id,
              values
            },
            suggestions: constraint.autoFix?.enabled ? [constraint.autoFix.fixMessage] : []
          };

          if (constraint.severity === 'error') {
            errors.push(error);
          } else {
            warnings.push(error);
          }
        }
      } catch (error) {
        console.error(`Error evaluating constraint ${constraint.id}:`, error);
        errors.push({
          id: `${constraint.id}_eval_error`,
          type: 'invalid_formula',
          message: `约束验证失败: ${error.message}`,
          field: constraint.expression,
          severity: 'error',
          context: {
            componentId,
            constraintId: constraint.id,
            values
          },
          suggestions: []
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: [],
      validationTime: new Date().toISOString(),
      summary: {
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalSuggestions: 0,
        criticalErrors: errors.filter(e => e.severity === 'error').length
      }
    };
  }

  private evaluateConstraint(expression: string, values: Record<string, any>): boolean {
    // 简化的约束评估逻辑
    // 实际应用中需要使用专业的表达式解析器
    try {
      // 替换变量名为实际值
      let evaluableExpression = expression;
      for (const [key, value] of Object.entries(values)) {
        evaluableExpression = evaluableExpression.replace(
          new RegExp(`\\b${key}\\b`, 'g'),
          String(value)
        );
      }
      
      // 简单的安全检查
      if (!/^[0-9+\-*/.()>\s<>=&|!]+$/.test(evaluableExpression)) {
        return true; // 如果表达式包含不安全字符，默认通过
      }

      return eval(evaluableExpression);
    } catch {
      return true; // 如果评估失败，默认通过
    }
  }

  async calculateQuantity(componentId: string, parameters: Record<string, any>): Promise<number> {
    const component = await this.getComponentById(componentId);
    if (!component || !component.quantityFormula) {
      return 1;
    }

    try {
      // 简化的公式计算逻辑
      let formula = component.quantityFormula;
      for (const [key, value] of Object.entries(parameters)) {
        formula = formula.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
      }
      
      return eval(formula) || 1;
    } catch (error) {
      console.error(`Error calculating quantity for component ${componentId}:`, error);
      return 1;
    }
  }

  async calculateCost(componentId: string, parameters: Record<string, any>): Promise<number> {
    const component = await this.getComponentById(componentId);
    if (!component || !component.costFormula) {
      return 0;
    }

    try {
      // 简化的成本计算逻辑
      let formula = component.costFormula;
      for (const [key, value] of Object.entries(parameters)) {
        formula = formula.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
      }

      return eval(formula) || 0;
    } catch (error) {
      console.error(`Error calculating cost for component ${componentId}:`, error);
      return 0;
    }
  }
}

// 构件服务
export class AssemblyService {
  async getAssemblies(filters?: AssemblyFilters): Promise<Assembly[]> {
    const data = await MockDataLoader.loadMockData<{ assemblies: Assembly[] }>('assemblies.json');
    let assemblies = data.assemblies;

    if (filters) {
      assemblies = assemblies.filter(assembly => {
        if (filters.assemblyType && filters.assemblyType.length > 0 && !filters.assemblyType.includes(assembly.assemblyType)) {
          return false;
        }
        if (filters.status && filters.status.length > 0 && !filters.status.includes(assembly.status)) {
          return false;
        }
        if (filters.keyword) {
          const searchLower = filters.keyword.toLowerCase();
          const matchesKeyword = (
            assembly.name.toLowerCase().includes(searchLower) ||
            assembly.code.toLowerCase().includes(searchLower) ||
            assembly.description?.toLowerCase().includes(searchLower)
          );
          if (!matchesKeyword) {
            return false;
          }
        }
        if (filters.tags && filters.tags.length > 0) {
          const assemblyTags = assembly.properties?.tags || [];
          const hasMatchingTag = filters.tags.some(tag => assemblyTags.includes(tag));
          if (!hasMatchingTag) {
            return false;
          }
        }
        return true;
      });
    }

    return assemblies;
  }

  async getAssemblyById(id: string): Promise<Assembly | null> {
    const assemblies = await this.getAssemblies();
    return assemblies.find(a => a.id === id) || null;
  }

  async createAssembly(assembly: Partial<Assembly>): Promise<Assembly> {
    const newAssembly: Assembly = {
      id: `asm_${Date.now()}`,
      code: assembly.code || '',
      name: assembly.name || '',
      description: assembly.description,
      assemblyType: assembly.assemblyType || 'complete_assembly',
      componentInstances: assembly.componentInstances || [],
      subAssemblies: assembly.subAssemblies || [],
      assemblyParameters: assembly.assemblyParameters || [],
      assemblyConstraints: assembly.assemblyConstraints || [],
      assemblyProcess: assembly.assemblyProcess || {
        id: `proc_${Date.now()}`,
        processName: 'Default Process',
        steps: [],
        totalEstimatedTime: 0
      },
      qualityRequirements: assembly.qualityRequirements || [],
      status: assembly.status || 'draft',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Creating assembly:', newAssembly);
    return newAssembly;
  }

  async updateAssembly(id: string, updates: Partial<Assembly>): Promise<Assembly> {
    const assembly = await this.getAssemblyById(id);
    if (!assembly) {
      throw new Error(`Assembly with id ${id} not found`);
    }

    const updatedAssembly: Assembly = {
      ...assembly,
      ...updates,
      id: assembly.id,
      version: assembly.version + 1,
      updatedAt: new Date().toISOString(),
      updatedBy: 'current_user'
    };

    console.log('Updating assembly:', updatedAssembly);
    return updatedAssembly;
  }

  async deleteAssembly(id: string): Promise<void> {
    console.log('Deleting assembly:', id);
  }
}

// 产品结构服务
export class ProductStructureService {
  async getStructures(filters?: ProductStructureFilters): Promise<ProductStructure[]> {
    const data = await MockDataLoader.loadMockData<{ productStructures: ProductStructure[] }>('product-structures.json');
    let structures = data.productStructures;

    if (filters) {
      structures = structures.filter(structure => {
        if (filters.productType && filters.productType.length > 0 && !filters.productType.includes(structure.productType)) {
          return false;
        }
        if (filters.category && filters.category.length > 0 && !filters.category.includes(structure.category)) {
          return false;
        }
        if (filters.status && filters.status.length > 0 && !filters.status.includes(structure.status)) {
          return false;
        }
        if (filters.keyword) {
          const searchLower = filters.keyword.toLowerCase();
          const matchesKeyword = (
            structure.name.toLowerCase().includes(searchLower) ||
            structure.code.toLowerCase().includes(searchLower) ||
            structure.description?.toLowerCase().includes(searchLower)
          );
          if (!matchesKeyword) {
            return false;
          }
        }
        if (filters.tags && filters.tags.length > 0) {
          const hasMatchingTag = filters.tags.some(tag => structure.tags.includes(tag));
          if (!hasMatchingTag) {
            return false;
          }
        }
        return true;
      });
    }

    return structures;
  }

  async getStructureById(id: string): Promise<ProductStructure | null> {
    const structures = await this.getStructures();
    return structures.find(s => s.id === id) || null;
  }

  async createStructure(structure: Partial<ProductStructure>): Promise<ProductStructure> {
    const newStructure: ProductStructure = {
      id: `struct_${Date.now()}`,
      code: structure.code || '',
      name: structure.name || '',
      description: structure.description,
      productType: structure.productType || 'other',
      category: structure.category || '',
      subCategory: structure.subCategory || '',
      rootAssembly: structure.rootAssembly || {
        id: `asm_inst_${Date.now()}`,
        assemblyId: '',
        assemblyCode: '',
        assemblyName: '',
        assemblyVersion: 1,
        instanceName: 'Root Assembly',
        quantity: 1,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        parameterValues: {},
        optional: false,
        alternatives: [],
        properties: {}
      },
      productParameters: structure.productParameters || [],
      productConstraints: structure.productConstraints || [],
      configurationOptions: structure.configurationOptions || [],
      versionHistory: structure.versionHistory || [],
      applications: structure.applications || [],
      tags: structure.tags || [],
      properties: structure.properties || {},
      status: structure.status || 'draft',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Creating product structure:', newStructure);
    return newStructure;
  }

  async updateStructure(id: string, updates: Partial<ProductStructure>): Promise<ProductStructure> {
    const structure = await this.getStructureById(id);
    if (!structure) {
      throw new Error(`Product structure with id ${id} not found`);
    }

    const updatedStructure: ProductStructure = {
      ...structure,
      ...updates,
      id: structure.id, // 确保ID不被覆盖
      version: structure.version + 1,
      updatedAt: new Date().toISOString(),
      updatedBy: 'current_user'
    };

    console.log('Updating product structure:', updatedStructure);
    return updatedStructure;
  }

  async deleteStructure(id: string): Promise<void> {
    console.log('Deleting product structure:', id);
    // 在实际应用中，这里会调用API删除
  }

  async applyConfiguration(structureId: string, config: Record<string, any>): Promise<ProductStructure> {
    const structure = await this.getStructureById(structureId);
    if (!structure) {
      throw new Error(`Product structure with id ${structureId} not found`);
    }

    // 应用配置逻辑
    const updatedStructure = { ...structure };

    // 更新产品参数值
    for (const [key, value] of Object.entries(config)) {
      const parameter = updatedStructure.productParameters.find(p => p.name === key);
      if (parameter) {
        parameter.defaultValue = value;
      }
    }

    // 应用配置选项
    for (const configOption of updatedStructure.configurationOptions) {
      for (const choice of configOption.choices) {
        if (config[configOption.name] === choice.value) {
          // 应用组件变更
          for (const componentChange of choice.componentChanges) {
            // 这里需要实现具体的组件变更逻辑
            console.log('Applying component change:', componentChange);
          }

          // 应用构件变更
          for (const assemblyChange of choice.assemblyChanges) {
            // 这里需要实现具体的构件变更逻辑
            console.log('Applying assembly change:', assemblyChange);
          }
        }
      }
    }

    updatedStructure.version += 1;
    updatedStructure.updatedAt = new Date().toISOString();
    updatedStructure.updatedBy = 'current_user';

    console.log('Applied configuration to structure:', updatedStructure);
    return updatedStructure;
  }

  async validateConfiguration(structureId: string, config: Record<string, any>): Promise<ValidationResult> {
    const structure = await this.getStructureById(structureId);
    if (!structure) {
      throw new Error(`Product structure with id ${structureId} not found`);
    }

    const errors: any[] = [];
    const warnings: any[] = [];

    // 验证产品级约束
    for (const constraint of structure.productConstraints) {
      try {
        const isValid = this.evaluateConstraint(constraint.expression, config);
        if (!isValid) {
          const error = {
            id: constraint.id,
            type: constraint.type,
            message: constraint.errorMessage,
            field: constraint.expression,
            severity: constraint.severity,
            context: {
              structureId,
              constraintId: constraint.id,
              config
            },
            suggestions: constraint.autoFix?.enabled ? [constraint.autoFix.fixMessage] : []
          };

          if (constraint.severity === 'error') {
            errors.push(error);
          } else {
            warnings.push(error);
          }
        }
      } catch (error) {
        console.error(`Error evaluating constraint ${constraint.id}:`, error);
        errors.push({
          id: `${constraint.id}_eval_error`,
          type: 'invalid_formula',
          message: `约束验证失败: ${error.message}`,
          field: constraint.expression,
          severity: 'error',
          context: {
            structureId,
            constraintId: constraint.id,
            config
          },
          suggestions: []
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: [],
      validationTime: new Date().toISOString(),
      summary: {
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalSuggestions: 0,
        criticalErrors: errors.filter(e => e.severity === 'error').length
      }
    };
  }

  async generateBOM(structureId: string, config?: Record<string, any>): Promise<BOMItem[]> {
    const structure = await this.getStructureById(structureId);
    if (!structure) {
      throw new Error(`Product structure with id ${structureId} not found`);
    }

    const bomItems: BOMItem[] = [];

    // 递归生成BOM项目
    await this.generateBOMFromAssembly(structure.rootAssembly, bomItems, 0, config || {});

    return bomItems;
  }

  async exportBOM(structureId: string, format: 'excel' | 'pdf' | 'csv'): Promise<Blob> {
    const bomItems = await this.generateBOM(structureId);

    // 这里应该实现实际的导出逻辑
    // 目前返回一个模拟的Blob
    const data = JSON.stringify(bomItems, null, 2);
    return new Blob([data], { type: 'application/json' });
  }

  private async generateBOMFromAssembly(
    assemblyInstance: any,
    bomItems: BOMItem[],
    level: number,
    config: Record<string, any>
  ): Promise<void> {
    // 获取构件定义
    const assemblyService = new AssemblyService();
    const assembly = await assemblyService.getAssemblyById(assemblyInstance.assemblyId);

    if (!assembly) {
      return;
    }

    // 处理组件实例
    for (const componentInstance of assembly.componentInstances) {
      const componentService = new ComponentService();
      const component = await componentService.getComponentById(componentInstance.componentId);

      if (component) {
        const quantity = await componentService.calculateQuantity(
          component.id,
          { ...config, ...componentInstance.parameterValues }
        );

        bomItems.push({
          id: `bom_${Date.now()}_${Math.random()}`,
          level,
          parentId: level > 0 ? assemblyInstance.id : undefined,
          itemType: 'component',
          itemId: component.id,
          itemCode: component.code,
          itemName: component.name,
          itemDescription: component.description,
          materialCategoryId: component.materialCategoryId,
          materialCategoryName: component.materialCategoryName,
          materialCategoryCode: component.materialCategoryCode,
          quantity,
          unit: 'pcs', // 应该从组件定义中获取
          unitCost: 0, // 应该计算实际成本
          totalCost: 0,
          specifications: component.properties,
          remarks: componentInstance.instanceName,
          optional: componentInstance.optional,
          children: []
        });
      }
    }

    // 递归处理子构件
    for (const subAssemblyInstance of assembly.subAssemblies) {
      await this.generateBOMFromAssembly(subAssemblyInstance, bomItems, level + 1, config);
    }
  }

  private evaluateConstraint(expression: string, values: Record<string, any>): boolean {
    // 简化的约束评估逻辑
    try {
      let evaluableExpression = expression;
      for (const [key, value] of Object.entries(values)) {
        evaluableExpression = evaluableExpression.replace(
          new RegExp(`\\b${key}\\b`, 'g'),
          String(value)
        );
      }

      // 简单的安全检查
      if (!/^[0-9+\-*/.()>\s<>=&|!]+$/.test(evaluableExpression)) {
        return true; // 如果表达式包含不安全字符，默认通过
      }

      return eval(evaluableExpression);
    } catch {
      return true; // 如果评估失败，默认通过
    }
  }
}

// 产品服务
export class ProductService {
  async getProducts(filters?: ProductFilters): Promise<Product[]> {
    const data = await MockDataLoader.loadMockData<{ products: Product[] }>('products.json');
    let products = data.products;

    if (filters) {
      products = products.filter(product => {
        if (filters.productStructureId && product.productStructureId !== filters.productStructureId) {
          return false;
        }
        if (filters.category && product.category !== filters.category) {
          return false;
        }
        if (filters.lifecycle && product.lifecycle !== filters.lifecycle) {
          return false;
        }
        if (filters.status && product.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            product.name.toLowerCase().includes(searchLower) ||
            product.code.toLowerCase().includes(searchLower) ||
            product.description?.toLowerCase().includes(searchLower)
          );
        }
        if (filters.tags && filters.tags.length > 0) {
          return filters.tags.some(tag => product.tags.includes(tag));
        }
        return true;
      });
    }

    return products;
  }

  async getProductById(id: string): Promise<Product | null> {
    const products = await this.getProducts();
    return products.find(p => p.id === id) || null;
  }

  async createProduct(product: Partial<Product>): Promise<Product> {
    const newProduct: Product = {
      id: `prod_${Date.now()}`,
      code: product.code || '',
      name: product.name || '',
      description: product.description,
      productStructureId: product.productStructureId || '',
      productStructureCode: product.productStructureCode || '',
      productStructureName: product.productStructureName || '',
      productStructureVersion: product.productStructureVersion || 1,
      category: product.category || '',
      subCategory: product.subCategory,
      lifecycle: product.lifecycle || 'design',
      componentMaterialMap: product.componentMaterialMap || [],
      defaultParameters: product.defaultParameters || {},
      status: product.status || 'draft',
      tags: product.tags || [],
      documents: product.documents || [],
      statistics: product.statistics || {
        quoteBOMCount: 0,
        productionBOMCount: 0,
        orderCount: 0
      },
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Creating product:', newProduct);
    return newProduct;
  }
}

// 报价BOM服务
export class QuoteBOMService {
  async getQuoteBOMs(filters?: QuoteBOMFilters): Promise<QuoteBOM[]> {
    const data = await MockDataLoader.loadMockData<{ quoteBOMs: QuoteBOM[] }>('quote-boms.json');
    let boms = data.quoteBOMs;

    if (filters) {
      boms = boms.filter(bom => {
        if (filters.productId && bom.productId !== filters.productId) {
          return false;
        }
        if (filters.status && bom.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            bom.name.toLowerCase().includes(searchLower) ||
            bom.code.toLowerCase().includes(searchLower) ||
            bom.productName.toLowerCase().includes(searchLower)
          );
        }
        if (filters.costRange) {
          const [minCost, maxCost] = filters.costRange;
          return bom.costSummary.totalCost >= minCost && bom.costSummary.totalCost <= maxCost;
        }
        return true;
      });
    }

    return boms;
  }

  async getQuoteBOMById(id: string): Promise<QuoteBOM | null> {
    const boms = await this.getQuoteBOMs();
    return boms.find(b => b.id === id) || null;
  }

  async generateQuoteBOM(productId: string, configuration: ProductConfiguration): Promise<QuoteBOM> {
    const product = await productService.getProductById(productId);
    if (!product) {
      throw new Error(`Product with id ${productId} not found`);
    }

    // 简化的BOM生成逻辑
    const bomItems = await this.generateBOMItems(product, configuration);
    const costSummary = this.calculateCostSummary(bomItems);

    const quoteBOM: QuoteBOM = {
      id: `qbom_${Date.now()}`,
      code: `QB-${Date.now()}`,
      name: `${product.name}-报价BOM`,
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      productVersion: product.version,
      configurationSnapshot: configuration.parameterValues,
      items: bomItems,
      costSummary,
      status: 'draft',
      validFrom: new Date().toISOString(),
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Generated quote BOM:', quoteBOM);
    return quoteBOM;
  }

  private async generateBOMItems(product: Product, configuration: ProductConfiguration) {
    // 简化的BOM项目生成逻辑
    const items = [];

    for (const mapping of product.componentMaterialMap) {
      const component = await componentService.getComponentById(mapping.componentId);
      if (!component) continue;

      const quantity = await componentService.calculateQuantity(
        component.id,
        configuration.parameterValues
      );

      items.push({
        id: `qitem_${Date.now()}_${Math.random()}`,
        level: 1,
        componentId: component.id,
        componentCode: component.code,
        componentName: component.name,
        materialCategoryId: mapping.materialCategoryId,
        materialCategoryName: mapping.materialCategoryName,
        quantity,
        unit: 'pcs',
        unitCost: 100, // 简化的成本计算
        totalCost: quantity * 100,
        wastageRate: 5,
        actualQuantity: quantity * 1.05,
        optional: false
      });
    }

    return items;
  }

  private calculateCostSummary(items: any[]) {
    const materialCost = items.reduce((sum, item) => sum + item.totalCost, 0);
    const laborCost = materialCost * 0.3;
    const overheadCost = materialCost * 0.13;
    const totalCost = materialCost + laborCost + overheadCost;

    return {
      materialCost,
      laborCost,
      overheadCost,
      totalCost
    };
  }
}

// 生产BOM服务
export class ProductionBOMService {
  async getProductionBOMs(filters?: ProductionBOMFilters): Promise<ProductionBOM[]> {
    const data = await MockDataLoader.loadMockData<{ productionBOMs: ProductionBOM[] }>('production-boms.json');
    let boms = data.productionBOMs;

    if (filters) {
      boms = boms.filter(bom => {
        if (filters.productId && bom.productId !== filters.productId) {
          return false;
        }
        if (filters.quoteBOMId && bom.quoteBOMId !== filters.quoteBOMId) {
          return false;
        }
        if (filters.status && bom.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            bom.name.toLowerCase().includes(searchLower) ||
            bom.code.toLowerCase().includes(searchLower) ||
            bom.productName.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    }

    return boms;
  }

  async getProductionBOMById(id: string): Promise<ProductionBOM | null> {
    const boms = await this.getProductionBOMs();
    return boms.find(b => b.id === id) || null;
  }

  async convertFromQuoteBOM(quoteBOMId: string): Promise<ProductionBOM> {
    const quoteBOM = await quoteBOMService.getQuoteBOMById(quoteBOMId);
    if (!quoteBOM) {
      throw new Error(`Quote BOM with id ${quoteBOMId} not found`);
    }

    // 简化的转换逻辑
    const productionItems = quoteBOM.items.map(quoteItem => ({
      ...quoteItem,
      id: `pitem_${Date.now()}_${Math.random()}`,
      materialVariantId: `mv_${Math.random()}`,
      materialVariantCode: `VARIANT_${quoteItem.componentCode}`,
      materialVariantName: `${quoteItem.componentName} - 具体变体`,
      alternativeMaterials: [],
      stockInfo: {
        availableQuantity: 100,
        reservedQuantity: 0,
        shortageQuantity: 0
      },
      procurementInfo: {
        leadTime: 7,
        minOrderQuantity: 1,
        supplierCode: 'SUP_001'
      }
    }));

    const productionBOM: ProductionBOM = {
      id: `pbom_${Date.now()}`,
      code: `PB-${Date.now()}`,
      name: `${quoteBOM.productName}-生产BOM`,
      productId: quoteBOM.productId,
      productCode: quoteBOM.productCode,
      productName: quoteBOM.productName,
      productVersion: quoteBOM.productVersion,
      quoteBOMId: quoteBOM.id,
      quoteBOMCode: quoteBOM.code,
      quoteBOMVersion: quoteBOM.version,
      configurationSnapshot: quoteBOM.configurationSnapshot,
      items: productionItems,
      costSummary: quoteBOM.costSummary,
      status: 'draft',
      effectiveFrom: new Date().toISOString(),
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Converted to production BOM:', productionBOM);
    return productionBOM;
  }
}

// 验证服务
export class ValidationService {
  async validateStructure(structure: ProductStructure): Promise<StructureValidationResult> {
    const errors: any[] = [];
    const warnings: any[] = [];
    const suggestions: any[] = [];

    // 检查结构完整性
    const structureIntegrity = await this.checkStructureIntegrity(structure);

    // 检查约束冲突
    const constraintValidation = await this.validateConstraints(structure.productConstraints, {});
    errors.push(...constraintValidation.errors);
    warnings.push(...constraintValidation.warnings);

    // 检查循环引用
    const circularReferences = await this.checkCircularReferences(structure);
    if (circularReferences.length > 0) {
      errors.push(...circularReferences);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      validationTime: new Date().toISOString(),
      summary: {
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalSuggestions: suggestions.length,
        criticalErrors: errors.filter(e => e.severity === 'error').length
      },
      structureIntegrity,
      structureStatistics: {
        totalComponents: await this.countComponents(structure),
        totalAssemblies: await this.countAssemblies(structure)
      }
    };
  }

  async validateConstraints(constraints: any[], values: Record<string, any>): Promise<ValidationResult> {
    const errors: any[] = [];
    const warnings: any[] = [];

    for (const constraint of constraints) {
      try {
        const isValid = this.evaluateConstraint(constraint.expression, values);
        if (!isValid) {
          const error = {
            id: constraint.id,
            type: constraint.type,
            message: constraint.errorMessage,
            field: constraint.expression,
            severity: constraint.severity,
            context: {
              constraintId: constraint.id,
              values
            },
            suggestions: constraint.autoFix?.enabled ? [constraint.autoFix.fixMessage] : []
          };

          if (constraint.severity === 'error') {
            errors.push(error);
          } else {
            warnings.push(error);
          }
        }
      } catch (error) {
        errors.push({
          id: `${constraint.id}_eval_error`,
          type: 'invalid_formula',
          message: `约束验证失败: ${error.message}`,
          field: constraint.expression,
          severity: 'error',
          context: {
            constraintId: constraint.id,
            values
          },
          suggestions: []
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: [],
      validationTime: new Date().toISOString(),
      summary: {
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalSuggestions: 0,
        criticalErrors: errors.filter(e => e.severity === 'error').length
      }
    };
  }

  async resolveConstraints(constraints: any[], values: Record<string, any>): Promise<Record<string, any>> {
    // 简化的约束求解实现
    const resolvedValues = { ...values };

    for (const constraint of constraints) {
      if (constraint.autoFix?.enabled) {
        try {
          // 尝试应用自动修复
          const fixResult = this.applyAutoFix(constraint, resolvedValues);
          Object.assign(resolvedValues, fixResult);
        } catch (error) {
          console.error(`Failed to apply auto fix for constraint ${constraint.id}:`, error);
        }
      }
    }

    return resolvedValues;
  }

  async suggestFixes(errors: any[]): Promise<any[]> {
    const suggestions: any[] = [];

    for (const error of errors) {
      if (error.suggestions && error.suggestions.length > 0) {
        suggestions.push({
          id: `fix_${error.id}`,
          errorId: error.id,
          type: 'auto_fix',
          title: '自动修复建议',
          description: error.suggestions[0],
          action: {
            type: 'parameter_change',
            parameters: {}
          },
          confidence: 0.8,
          impact: 'low'
        });
      }
    }

    return suggestions;
  }

  private async checkStructureIntegrity(structure: ProductStructure): Promise<any> {
    return {
      hasMissingComponents: false,
      hasCircularReferences: false,
      hasInvalidReferences: false,
      missingComponents: [],
      circularReferences: [],
      invalidReferences: []
    };
  }

  private async checkCircularReferences(structure: ProductStructure): Promise<any[]> {
    // 简化的循环引用检查
    return [];
  }

  private async countComponents(structure: ProductStructure): Promise<number> {
    // 简化的组件计数
    return 0;
  }

  private async countAssemblies(structure: ProductStructure): Promise<number> {
    // 简化的构件计数
    return 1;
  }

  private evaluateConstraint(expression: string, values: Record<string, any>): boolean {
    try {
      let evaluableExpression = expression;
      for (const [key, value] of Object.entries(values)) {
        evaluableExpression = evaluableExpression.replace(
          new RegExp(`\\b${key}\\b`, 'g'),
          String(value)
        );
      }

      if (!/^[0-9+\-*/.()>\s<>=&|!]+$/.test(evaluableExpression)) {
        return true;
      }

      return eval(evaluableExpression);
    } catch {
      return true;
    }
  }

  private applyAutoFix(constraint: any, values: Record<string, any>): Record<string, any> {
    // 简化的自动修复实现
    return {};
  }
}

// 导出服务实例
export const componentService = new ComponentService();
export const assemblyService = new AssemblyService();
export const productStructureService = new ProductStructureService();
export const productService = new ProductService();
export const quoteBOMService = new QuoteBOMService();
export const productionBOMService = new ProductionBOMService();
export const validationService = new ValidationService();
