import { defineStore } from 'pinia';

interface SchedulingState {
  unscheduledOrders: any[];
  productionPlan: any[];
  isLoading: boolean;
  error: string | null;
}

export const useSchedulingStore = defineStore('scheduling', {
  state: (): SchedulingState => ({
    unscheduledOrders: [],
    productionPlan: [],
    isLoading: false,
    error: null,
  }),

  getters: {
    urgentOrders: (state) => 
      state.unscheduledOrders.filter(order => order.priority === 'urgent'),
    
    pendingOrderCount: (state) => state.unscheduledOrders.length,
  },

  actions: {
    async fetchUnscheduledOrders() {
      this.isLoading = true;
      this.error = null;
      try {
        // In a real app, you would fetch this from an API
        // For now, we'll use mock data.
        const mockOrders = [
          { id: 'SO-001', customer: '远大幕墙', product: '5+12A+5 Low-E (80片)', deadline: '明天 17:00 前', priority: 'urgent' },
          { id: 'SO-002', customer: '城市之窗', product: '8mm钢化 (120片)', deadline: '2025-08-18', priority: 'normal' },
          { id: 'SO-003', customer: '碧桂园', product: '6mm白玻 (300片)', deadline: '2025-08-20', priority: 'low' },
        ];
        this.unscheduledOrders = mockOrders;
      } catch (e) {
        this.error = '无法加载待处理订单。';
        console.error(e);
      } finally {
        this.isLoading = false;
      }
    },

    runIntelligentScheduling(selectedOrderIds: string[]) {
      // This action will trigger the complex backend process.
      // For the prototype, we can simulate the result.
      console.log('Running intelligent scheduling for orders:', selectedOrderIds);
      
      // 1. Remove selected orders from the unscheduled list
      const ordersToSchedule = this.unscheduledOrders.filter(order => selectedOrderIds.includes(order.id));
      this.unscheduledOrders = this.unscheduledOrders.filter(order => !selectedOrderIds.includes(order.id));

      // 2. Simulate creating a production batch and adding it to the plan
      const newBatch = {
        id: `BATCH-${Date.now().toString().slice(-4)}`,
        orders: ordersToSchedule.map(o => o.id),
        startTime: '08:00',
        endTime: '11:30',
        date: '今天',
      };

      this.productionPlan.push(newBatch);
      
      console.log('New production plan:', this.productionPlan);
    },
  },
});