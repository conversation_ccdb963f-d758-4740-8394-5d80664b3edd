/**
 * 产品结构管理系统 - 核心类型定义
 * 
 * 基于"组件(Component)→构件(Assembly)→产品结构(ProductStructure)"三层架构
 * 支持参数化设计、约束管理、版本控制和BOM生成
 */

// ============================================================================
// 基础类型定义
// ============================================================================

/** 基础实体接口 - 所有业务实体的通用属性 */
export interface BaseEntity {
  /** 唯一标识符 */
  id: string;
  /** 业务编码 */
  code: string;
  /** 名称 */
  name: string;
  /** 描述信息 */
  description?: string;
  /** 版本号 */
  version: number;
  /** 状态：草稿、活跃、已弃用、已归档 */
  status: 'draft' | 'active' | 'deprecated' | 'archived';
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 创建人 */
  createdBy: string;
  /** 更新人 */
  updatedBy: string;
}

/** 参数类型枚举 */
export type ParameterType = 'number' | 'string' | 'boolean' | 'select' | 'formula';

/** 参数分类枚举 */
export type ParameterCategory = 'dimension' | 'material' | 'process' | 'quality';

/** 约束类型枚举 */
export type ConstraintType = 'dimension' | 'material' | 'process' | 'compatibility';

/** 严重级别枚举 */
export type SeverityLevel = 'error' | 'warning' | 'info';

// ============================================================================
// 参数化设计相关类型
// ============================================================================

/** 参数选项定义 */
export interface ParameterOption {
  /** 选项值 */
  value: string | number | boolean;
  /** 显示标签 */
  label: string;
  /** 选项描述 */
  description?: string;
  /** 附加成本 */
  additionalCost?: number;
  /** 材料影响 */
  materialImpact?: Record<string, any>;
}

/** 组件参数定义 */
export interface ComponentParameter {
  /** 参数ID */
  id: string;
  /** 参数名称（用于计算） */
  name: string;
  /** 显示名称 */
  displayName: string;
  /** 参数类型 */
  type: ParameterType;
  /** 单位 */
  unit?: string;
  /** 默认值 */
  defaultValue?: any;
  /** 最小值（数值类型） */
  minValue?: number;
  /** 最大值（数值类型） */
  maxValue?: number;
  /** 选项列表（选择类型） */
  options?: ParameterOption[];
  /** 是否必填 */
  required: boolean;
  /** 参数描述 */
  description?: string;
  /** 参数分类 */
  category: ParameterCategory;
  /** 是否在界面显示 */
  visible: boolean;
  /** 是否可编辑 */
  editable: boolean;
  /** 验证规则 */
  validationRules?: string[];
}

/** 约束自动修复配置 */
export interface ConstraintAutoFix {
  /** 是否启用自动修复 */
  enabled: boolean;
  /** 修复表达式 */
  fixExpression: string;
  /** 修复提示信息 */
  fixMessage: string;
}

/** 组件约束定义 */
export interface ComponentConstraint {
  /** 约束ID */
  id: string;
  /** 约束名称 */
  name: string;
  /** 约束类型 */
  type: ConstraintType;
  /** 约束表达式 */
  expression: string;
  /** 错误信息 */
  errorMessage: string;
  /** 严重级别 */
  severity: SeverityLevel;
  /** 自动修复配置 */
  autoFix?: ConstraintAutoFix;
  /** 是否启用 */
  enabled: boolean;
  /** 约束描述 */
  description?: string;
}

// ============================================================================
// 工艺管理相关类型
// ============================================================================

/** 工艺类型枚举 */
export type ProcessType = 'cutting' | 'drilling' | 'assembly' | 'welding' | 'coating' | 'testing' | 'other';

/** 技能等级枚举 */
export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

/** 工艺要求定义 */
export interface ProcessRequirement {
  /** 工艺ID */
  id: string;
  /** 工艺类型 */
  processType: ProcessType;
  /** 工艺名称 */
  name: string;
  /** 工艺参数 */
  parameters: Record<string, any>;
  /** 所需设备 */
  requiredEquipment: string[];
  /** 技能等级要求 */
  skillLevel: SkillLevel;
  /** 预估时间（分钟） */
  estimatedTime: number;
  /** 工艺描述 */
  description?: string;
  /** 安全要求 */
  safetyRequirements?: string[];
  /** 质量检查点 */
  qualityCheckPoints?: string[];
}

/** 装配步骤定义 */
export interface AssemblyStep {
  /** 步骤ID */
  id: string;
  /** 步骤编号 */
  stepNumber: number;
  /** 步骤名称 */
  stepName: string;
  /** 步骤描述 */
  description: string;
  /** 预估时间（分钟） */
  estimatedTime: number;
  /** 所需资源 */
  resources: string[];
  /** 质量检查点 */
  qualityCheckPoints?: QualityCheckPoint[];
  /** 前置步骤 */
  previousSteps: string[];
  /** 后续步骤 */
  nextSteps: string[];
}

/** 质量检查点定义 */
export interface QualityCheckPoint {
  /** 检查点ID */
  id: string;
  /** 检查点名称 */
  checkPoint: string;
  /** 公差要求 */
  tolerance: string;
  /** 检查方法 */
  checkMethod: string;
}

/** 装配工艺定义 */
export interface AssemblyProcess {
  /** 工艺ID */
  id: string;
  /** 工艺名称 */
  processName: string;
  /** 装配步骤 */
  steps: AssemblyStep[];
  /** 总预估时间（分钟） */
  totalEstimatedTime: number;
  /** 技能要求 */
  requiredSkills: string[];
  /** 安全要求 */
  safetyRequirements: string[];
  /** 工艺描述 */
  description?: string;
}

/** 质量要求定义 */
export interface QualityRequirement {
  /** 质量要求ID */
  id: string;
  /** 质量类别 */
  category: string;
  /** 质量要求描述 */
  requirement: string;
  /** 测试方法 */
  testMethod: string;
  /** 验收标准 */
  acceptanceCriteria: string;
  /** 是否必检 */
  mandatory: boolean;
  /** 检查频率 */
  checkFrequency?: string;
}

// ============================================================================
// 组件相关类型
// ============================================================================

/** 组件类型枚举 */
export type ComponentType = 'frame' | 'glass' | 'hardware' | 'seal' | 'other';

/** 组件定义 */
export interface Component extends BaseEntity {
  /** 组件类型 */
  componentType: ComponentType;
  /** 物料分类ID */
  materialCategoryId: string;
  /** 物料分类名称 */
  materialCategoryName: string;
  /** 物料分类编码 */
  materialCategoryCode: string;
  /** 组件参数 */
  parameters: ComponentParameter[];
  /** 数量计算公式 */
  quantityFormula: string;
  /** 成本计算公式 */
  costFormula?: string;
  /** 约束条件 */
  constraints: ComponentConstraint[];
  /** 工艺要求 */
  processRequirements: ProcessRequirement[];
  /** 扩展属性 */
  properties: Record<string, any>;
  /** 标签 */
  tags: string[];
  /** 是否可重用 */
  reusable: boolean;
}

/** 组件实例定义 */
/** 组件实例 - 构件中组件的具体实例 */
export interface ComponentInstance {
  /** 实例ID */
  id: string;
  /** 引用的组件ID */
  componentId: string;
  /** 组件编码（冗余存储，便于显示） */
  componentCode: string;
  /** 组件名称（冗余存储，便于显示） */
  componentName: string;
  /** 实例名称（在构件中的唯一标识） */
  instanceName: string;
  /** 实例描述 */
  description?: string;

  /** 参数配置 */
  parameterConfig: ComponentInstanceParameterConfig;

  /** 数量配置 */
  quantityConfig: ComponentInstanceQuantityConfig;

  /** 位置配置 */
  positionConfig?: ComponentInstancePositionConfig;

  /** 约束配置 */
  constraintConfig?: ComponentInstanceConstraintConfig;

  /** 是否可选组件 */
  optional: boolean;
  /** 替代选项 */
  alternatives?: ComponentAlternative[];

  /** 实例状态 */
  status: 'active' | 'inactive' | 'deprecated';
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
}

/** 组件实例参数配置 */
export interface ComponentInstanceParameterConfig {
  /** 参数值覆盖（覆盖组件默认值） */
  parameterOverrides: Record<string, any>;
  /** 参数绑定（绑定到构件参数） */
  parameterBindings: Record<string, string>;
  /** 参数公式（基于构件参数计算） */
  parameterFormulas: Record<string, string>;
  /** 参数验证状态 */
  validationStatus: Record<string, ParameterValidationResult>;
}

/** 组件实例数量配置 */
export interface ComponentInstanceQuantityConfig {
  /** 固定数量 */
  fixedQuantity?: number;
  /** 数量公式（基于构件参数计算） */
  quantityFormula?: string;
  /** 最小数量 */
  minQuantity?: number;
  /** 最大数量 */
  maxQuantity?: number;
  /** 数量单位 */
  unit?: string;
}

/** 组件实例位置配置 */
export interface ComponentInstancePositionConfig {
  /** 3D位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 旋转角度 */
  rotation?: {
    x: number;
    y: number;
    z: number;
  };
  /** 位置约束 */
  constraints?: PositionConstraint[];
}

/** 组件实例约束配置 */
export interface ComponentInstanceConstraintConfig {
  /** 与其他组件实例的关系约束 */
  relationshipConstraints: RelationshipConstraint[];
  /** 参数约束 */
  parameterConstraints: ParameterConstraint[];
  /** 几何约束 */
  geometryConstraints: GeometryConstraint[];
}

/** 组件替代选项 */
export interface ComponentAlternative {
  /** 替代组件ID */
  componentId: string;
  /** 替代组件名称 */
  componentName: string;
  /** 替代条件 */
  condition?: string;
  /** 参数映射 */
  parameterMapping: Record<string, string>;
}

/** 参数验证结果 */
export interface ParameterValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errorMessage?: string;
  /** 警告信息 */
  warningMessage?: string;
  /** 建议值 */
  suggestedValue?: any;
}
  /** 显示条件（当满足条件时显示） */
  displayCondition?: string;
  /** 替代选项 */
  alternatives?: string[];
  /** 实例描述 */
  description?: string;
}

// ============================================================================
// 构件相关类型
// ============================================================================

/** 构件类型枚举 */
export type AssemblyType = 'frame_assembly' | 'glass_assembly' | 'hardware_assembly' | 'complete_assembly';

/** 构件定义 */
export interface Assembly extends BaseEntity {
  /** 构件类型 */
  assemblyType: AssemblyType;
  /** 组件实例列表 */
  componentInstances: ComponentInstance[];
  /** 子构件实例列表 */
  subAssemblies: AssemblyInstance[];
  /** 构件级参数 */
  assemblyParameters: ComponentParameter[];
  /** 构件级约束 */
  assemblyConstraints: ComponentConstraint[];
  /** 装配工艺 */
  assemblyProcess: AssemblyProcess;
  /** 质量要求 */
  qualityRequirements: QualityRequirement[];
  /** 扩展属性 */
  properties: Record<string, any>;
  /** 标签 */
  tags: string[];
}

/** 构件实例定义 */
export interface AssemblyInstance {
  /** 实例ID */
  id: string;
  /** 引用的构件ID */
  assemblyId: string;
  /** 实例名称 */
  instanceName: string;
  /** 参数值 */
  parameterValues: Record<string, any>;
  /** 数量 */
  quantity: number;
  /** 数量计算公式 */
  quantityFormula?: string;
  /** 位置信息 */
  position?: {
    x: number;
    y: number;
    z: number;
  };
  /** 是否可选 */
  optional: boolean;
  /** 显示条件 */
  displayCondition?: string;
  /** 实例描述 */
  description?: string;
}

// ============================================================================
// 产品结构相关类型
// ============================================================================

/** 产品类型枚举 */
export type ProductType = 'partition' | 'window' | 'door' | 'curtain_wall' | 'other';

/** 配置选项类型枚举 */
export type ConfigurationType = 'single_select' | 'multi_select' | 'boolean' | 'range' | 'text';

/** 配置选择定义 */
export interface ConfigurationChoice {
  /** 选择ID */
  id: string;
  /** 选择值 */
  value: any;
  /** 显示标签 */
  label: string;
  /** 选择描述 */
  description?: string;
  /** 附加成本 */
  additionalCost?: number;
  /** 组件变更影响 */
  componentChanges: ComponentChange[];
  /** 构件变更影响 */
  assemblyChanges: AssemblyChange[];
}

/** 组件变更定义 */
export interface ComponentChange {
  /** 目标组件实例ID */
  componentInstanceId: string;
  /** 变更类型：添加、移除、修改参数 */
  changeType: 'add' | 'remove' | 'modify_parameter';
  /** 参数变更（修改参数时使用） */
  parameterChanges?: Record<string, any>;
  /** 新组件定义（添加时使用） */
  newComponent?: ComponentInstance;
}

/** 构件变更定义 */
export interface AssemblyChange {
  /** 目标构件实例ID */
  assemblyInstanceId: string;
  /** 变更类型：添加、移除、修改参数 */
  changeType: 'add' | 'remove' | 'modify_parameter';
  /** 参数变更（修改参数时使用） */
  parameterChanges?: Record<string, any>;
  /** 新构件定义（添加时使用） */
  newAssembly?: AssemblyInstance;
}

/** 配置选项定义 */
export interface ConfigurationOption {
  /** 配置选项ID */
  id: string;
  /** 选项名称 */
  name: string;
  /** 显示名称 */
  displayName: string;
  /** 配置类型 */
  type: ConfigurationType;
  /** 是否必选 */
  required: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 可选择项 */
  choices: ConfigurationChoice[];
  /** 选项描述 */
  description?: string;
  /** 显示条件 */
  displayCondition?: string;
  /** 选项分组 */
  group?: string;
  /** 排序权重 */
  sortOrder: number;
}
// ============================================================================
// 版本控制相关类型
// ============================================================================

/** 变更类型枚举 */
export type ChangeType = 'component' | 'assembly' | 'parameter' | 'constraint' | 'configuration';

/** 操作类型枚举 */
export type OperationType = 'create' | 'update' | 'delete';

/** 变更记录定义 */
export interface ChangeRecord {
  /** 变更ID */
  id: string;
  /** 变更类型 */
  changeType: ChangeType;
  /** 目标对象ID */
  targetId: string;
  /** 目标对象名称 */
  targetName: string;
  /** 操作类型 */
  operationType: OperationType;
  /** 变更前数据 */
  beforeData?: any;
  /** 变更后数据 */
  afterData?: any;
  /** 变更描述 */
  description: string;
  /** 变更时间 */
  changeTime: string;
  /** 变更人 */
  changedBy: string;
}

/** 版本历史定义 */
export interface VersionHistory {
  /** 版本ID */
  id: string;
  /** 版本号 */
  versionNumber: string;
  /** 变更日期 */
  changeDate: string;
  /** 变更人 */
  changedBy: string;
  /** 变更类型 */
  changeType: string;
  /** 变更描述 */
  changeDescription: string;
  /** 变更记录列表 */
  changeRecords: ChangeRecord[];
  /** 审批人 */
  approvedBy?: string;
  /** 审批日期 */
  approvedDate?: string;
  /** 审批意见 */
  approvalComments?: string;
  /** 是否当前版本 */
  isCurrent: boolean;
}

// ============================================================================
// 验证相关类型
// ============================================================================

/** 验证错误定义 */
export interface ValidationError {
  /** 错误ID */
  id: string;
  /** 错误类型 */
  type: 'missing_component' | 'invalid_constraint' | 'circular_reference' | 'invalid_formula' | 'parameter_conflict';
  /** 错误信息 */
  message: string;
  /** 错误位置 */
  location: {
    /** 对象类型 */
    objectType: 'component' | 'assembly' | 'product_structure';
    /** 对象ID */
    objectId: string;
    /** 字段名称 */
    fieldName?: string;
  };
  /** 严重级别 */
  severity: SeverityLevel;
  /** 修复建议 */
  suggestions?: string[];
}

/** 验证警告定义 */
export interface ValidationWarning {
  /** 警告ID */
  id: string;
  /** 警告类型 */
  type: 'unused_component' | 'deprecated_component' | 'performance_issue' | 'best_practice';
  /** 警告信息 */
  message: string;
  /** 警告位置 */
  location: {
    objectType: 'component' | 'assembly' | 'product_structure';
    objectId: string;
    fieldName?: string;
  };
  /** 改进建议 */
  suggestions: string[];
}

/** 优化建议定义 */
export interface OptimizationSuggestion {
  /** 建议ID */
  id: string;
  /** 优化类型 */
  type: 'cost_reduction' | 'performance_improvement' | 'maintainability' | 'reusability';
  /** 建议内容 */
  suggestion: string;
  /** 预期收益 */
  expectedBenefit: string;
  /** 实施难度 */
  implementationDifficulty: 'low' | 'medium' | 'high';
  /** 影响范围 */
  impactScope: string[];
}

/** 验证结果定义 */
export interface ValidationResult {
  /** 验证是否通过 */
  isValid: boolean;
  /** 错误列表 */
  errors: ValidationError[];
  /** 警告列表 */
  warnings: ValidationWarning[];
  /** 优化建议 */
  suggestions: OptimizationSuggestion[];
  /** 验证时间 */
  validationTime: string;
  /** 验证摘要 */
  summary: {
    totalErrors: number;
    totalWarnings: number;
    totalSuggestions: number;
    criticalErrors: number;
  };
}

/** 结构验证结果定义 */
export interface StructureValidationResult extends ValidationResult {
  /** 结构完整性检查 */
  structureIntegrity: {
    /** 是否有缺失组件 */
    hasMissingComponents: boolean;
    /** 是否有循环引用 */
    hasCircularReferences: boolean;
    /** 是否有无效约束 */
    hasInvalidConstraints: boolean;
    /** 是否有无效公式 */
    hasInvalidFormulas: boolean;
  };
  /** 性能分析 */
  performanceAnalysis: {
    /** 结构复杂度 */
    complexityScore: number;
    /** 层级深度 */
    maxDepth: number;
    /** 组件总数 */
    totalComponents: number;
    /** 构件总数 */
    totalAssemblies: number;
  };
}

/** 验证建议定义 */
export interface ValidationSuggestion {
  /** 建议ID */
  id: string;
  /** 目标错误ID */
  errorId: string;
  /** 建议类型 */
  type: 'auto_fix' | 'manual_fix' | 'alternative_approach';
  /** 建议描述 */
  description: string;
  /** 修复操作（自动修复时使用） */
  fixAction?: {
    /** 操作类型 */
    action: 'update_parameter' | 'remove_constraint' | 'add_component' | 'modify_formula';
    /** 操作参数 */
    parameters: Record<string, any>;
  };
}

// ============================================================================
// BOM相关类型
// ============================================================================

/** BOM项目定义 */
export interface BOMItem {
  /** BOM项目ID */
  id: string;
  /** 层级 */
  level: number;
  /** 父项目ID */
  parentId?: string;
  /** 组件ID */
  componentId: string;
  /** 组件名称 */
  componentName: string;
  /** 组件编码 */
  componentCode: string;
  /** 物料分类ID */
  materialCategoryId: string;
  /** 物料分类名称 */
  materialCategoryName: string;
  /** 具体物料ID（如果已选择） */
  materialId?: string;
  /** 具体物料名称 */
  materialName?: string;
  /** 具体物料编码 */
  materialCode?: string;
  materialCategoryCode?: string;
  /** 数量 */
  quantity: number;
  /** 单位 */
  unit: string;
  /** 单价 */
  unitPrice?: number;
  /** 总价 */
  totalPrice?: number;
  /** 备注 */
  remarks?: string;
  /** 是否可选 */
  optional: boolean;
  /** 子项目 */
  children: BOMItem[];
  itemType: 'component' | 'subcomponent';
  itemId: string;
  itemCode: string;
  itemName: string;
  itemDescription: string;
  unitCost: number;
  totalCost: number;
}

/** BOM汇总项定义 */
export interface BOMSummaryItem {
  /** 物料分类ID */
  materialCategoryId: string;
  /** 物料分类名称 */
  materialCategoryName: string;
  /** 具体物料ID */
  materialId?: string;
  /** 具体物料名称 */
  materialName?: string;
  /** 具体物料编码 */
  materialCode?: string;
  /** 汇总数量 */
  totalQuantity: number;
  /** 单位 */
  unit: string;
  /** 单价 */
  unitPrice?: number;
  /** 总价 */
  totalPrice?: number;
  /** 使用的组件列表 */
  usedInComponents: string[];
}

/** BOM导出格式枚举 */
export type BOMExportFormat = 'excel' | 'pdf' | 'csv' | 'json';

/** BOM导出选项 */
export interface BOMExportOptions {
  /** 导出格式 */
  format: BOMExportFormat;
  /** 是否包含层级结构 */
  includeHierarchy: boolean;
  /** 是否包含汇总 */
  includeSummary: boolean;
  /** 是否包含价格信息 */
  includePricing: boolean;
  /** 是否包含可选项 */
  includeOptional: boolean;
  /** 自定义字段 */
  customFields?: string[];
}

// ============================================================================
// 产品结构主要类型
// ============================================================================

/** 产品结构定义 */
export interface ProductStructure extends BaseEntity {
  /** 产品类型 */
  productType: ProductType;
  /** 产品类别 */
  category: string;
  /** 产品子类别 */
  subCategory: string;
  /** 根构件实例 */
  rootAssembly: AssemblyInstance;
  /** 产品级参数 */
  productParameters: ComponentParameter[];
  /** 产品级约束 */
  productConstraints: ComponentConstraint[];
  /** 配置选项 */
  configurationOptions: ConfigurationOption[];
  /** 版本历史 */
  versionHistory: VersionHistory[];
  /** 应用场景 */
  applications: string[];
  /** 标签 */
  tags: string[];
  /** 扩展属性 */
  properties: Record<string, any>;
  /** 最后验证结果 */
  lastValidationResult?: StructureValidationResult;
  /** 最后验证时间 */
  lastValidationTime?: string;
}

// ============================================================================
// 筛选和搜索相关类型
// ============================================================================

/** 产品结构筛选条件 */
export interface ProductStructureFilters {
  /** 关键词搜索 */
  keyword?: string;
  /** 产品类型 */
  productType?: ProductType[];
  /** 产品类别 */
  category?: string[];
  /** 状态 */
  status?: ('draft' | 'active' | 'deprecated' | 'archived')[];
  /** 创建时间范围 */
  createdDateRange?: {
    start: string;
    end: string;
  };
  /** 创建人 */
  createdBy?: string[];
  /** 标签 */
  tags?: string[];
  /** 应用场景 */
  applications?: string[];
}

/** 组件筛选条件 */
export interface ComponentFilters {
  /** 关键词搜索 */
  keyword?: string;
  /** 组件类型 */
  componentType?: ComponentType[];
  /** 物料分类 */
  materialCategoryId?: string[];
  /** 状态 */
  status?: ('draft' | 'active' | 'deprecated' | 'archived')[];
  /** 是否可重用 */
  reusable?: boolean;
  /** 标签 */
  tags?: string[];
}

/** 构件筛选条件 */
export interface AssemblyFilters {
  /** 关键词搜索 */
  keyword?: string;
  /** 构件类型 */
  assemblyType?: AssemblyType[];
  /** 状态 */
  status?: ('draft' | 'active' | 'deprecated' | 'archived')[];
  /** 标签 */
  tags?: string[];
}

// ============================================================================
// 统计分析相关类型
// ============================================================================

/** 产品结构统计信息 */
export interface ProductStructureStatistics {
  /** 产品结构总数 */
  totalStructures: number;
  /** 活跃结构数 */
  activeStructures: number;
  /** 各类型分布 */
  typeDistribution: Record<ProductType, number>;
  /** 各状态分布 */
  statusDistribution: Record<string, number>;
  /** 最近创建的结构 */
  recentStructures: ProductStructure[];
  /** 最常用的组件 */
  mostUsedComponents: Array<{
    componentId: string;
    componentName: string;
    usageCount: number;
  }>;
}

/** 组件使用统计 */
export interface ComponentUsageStatistics {
  /** 组件ID */
  componentId: string;
  /** 组件名称 */
  componentName: string;
  /** 使用频率 */
  usageFrequency: number;
  /** 使用的产品结构列表 */
  usedInStructures: Array<{
    structureId: string;
    structureName: string;
    usageCount: number;
  }>;
  /** 最后使用时间 */
  lastUsedTime: string;
}

/** 结构复杂度分析 */
export interface StructureComplexityAnalysis {
  /** 产品结构ID */
  structureId: string;
  /** 平均层级深度 */
  averageDepth: number;
  /** 最大层级深度 */
  maxDepth: number;
  /** 组件数量分布 */
  componentCountDistribution: Record<string, number>;
  /** 构件复杂度评分 */
  complexityScore: number;
  /** 复杂度等级 */
  complexityLevel: 'low' | 'medium' | 'high' | 'very_high';
}

// ============================================================================
// API响应类型
// ============================================================================

/** API响应基础类型 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 错误代码 */
  errorCode?: string;
  /** 响应时间戳 */
  timestamp: string;
}

/** 分页选项类型 */
export interface PaginationOptions {
  /** 页码（从1开始） */
  page?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/** 分页响应类型 */
export interface PaginatedResponse<T = any> {
  /** 数据列表 */
  items: T[];
  /** 总数量 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrevious: boolean;
}

// ============================================================================
// 构件管理重构 - 新增类型定义
// ============================================================================

/** 构件参数 - 影响整个构件的参数 */
export interface AssemblyParameter {
  /** 参数ID */
  id: string;
  /** 参数名称 */
  name: string;
  /** 显示名称 */
  displayName: string;
  /** 参数类型 */
  type: 'number' | 'string' | 'boolean' | 'select' | 'formula';
  /** 参数分类 */
  category: 'dimension' | 'performance' | 'material' | 'process' | 'other';
  /** 默认值 */
  defaultValue: any;
  /** 单位 */
  unit?: string;
  /** 是否必需 */
  required: boolean;
  /** 参数描述 */
  description?: string;

  /** 数值范围（仅数值类型） */
  range?: {
    min?: number;
    max?: number;
    step?: number;
  };
  /** 选项列表（仅选择类型） */
  options?: ParameterOption[];
  /** 计算公式（仅公式类型） */
  formula?: string;

  /** 影响的组件实例 */
  affectedInstances: string[];
  /** 参数传播规则 */
  propagationRules: ParameterPropagationRule[];
}

/** 构件约束 - 整体约束条件 */
export interface AssemblyConstraint {
  /** 约束ID */
  id: string;
  /** 约束名称 */
  name: string;
  /** 约束类型 */
  type: 'dimension' | 'performance' | 'compatibility' | 'business';
  /** 约束表达式 */
  expression: string;
  /** 错误信息 */
  errorMessage: string;
  /** 严重程度 */
  severity: 'error' | 'warning' | 'info';
  /** 是否启用 */
  enabled: boolean;
  /** 自动修复 */
  autoFix?: {
    enabled: boolean;
    fixExpression: string;
    fixMessage: string;
  };
}

/** 参数传播规则 */
export interface ParameterPropagationRule {
  /** 目标组件实例ID */
  targetInstanceId: string;
  /** 目标参数名 */
  targetParameterName: string;
  /** 传播类型 */
  propagationType: 'direct' | 'formula' | 'mapping';
  /** 传播表达式 */
  expression?: string;
  /** 参数映射 */
  mapping?: Record<string, any>;
}

/** 位置约束 */
export interface PositionConstraint {
  /** 约束类型 */
  type: 'fixed' | 'relative' | 'calculated';
  /** 约束表达式 */
  expression: string;
  /** 参考对象 */
  reference?: string;
}

/** 关系约束 */
export interface RelationshipConstraint {
  /** 约束ID */
  id: string;
  /** 关系类型 */
  type: 'adjacent' | 'connected' | 'aligned' | 'parallel' | 'perpendicular';
  /** 目标实例ID */
  targetInstanceId: string;
  /** 约束参数 */
  parameters: Record<string, any>;
}

/** 参数约束 */
export interface ParameterConstraint {
  /** 约束ID */
  id: string;
  /** 参数名 */
  parameterName: string;
  /** 约束类型 */
  type: 'range' | 'dependency' | 'formula';
  /** 约束表达式 */
  expression: string;
  /** 错误信息 */
  errorMessage: string;
}

/** 几何约束 */
export interface GeometryConstraint {
  /** 约束ID */
  id: string;
  /** 几何类型 */
  type: 'distance' | 'angle' | 'overlap' | 'clearance';
  /** 约束值 */
  value: number;
  /** 公差 */
  tolerance: number;
  /** 目标对象 */
  target?: string;
}

/** 性能指标 */
export interface PerformanceMetric {
  /** 指标ID */
  id: string;
  /** 指标名称 */
  name: string;
  /** 指标类型 */
  type: 'structural' | 'thermal' | 'acoustic' | 'fire' | 'energy';
  /** 指标值 */
  value: number;
  /** 单位 */
  unit: string;
  /** 计算方法 */
  calculationMethod?: string;
  /** 标准要求 */
  standardRequirement?: number;
}

/** 构件成本信息 */
export interface AssemblyCostInfo {
  /** 材料成本 */
  materialCost: number;
  /** 加工成本 */
  processingCost: number;
  /** 装配成本 */
  assemblyCost: number;
  /** 总成本 */
  totalCost: number;
  /** 成本单位 */
  currency: string;
  /** 成本计算时间 */
  calculatedAt: string;
}

/** 简单验证项（用于UI组件） */
export interface SimpleValidationItem {
  /** 验证类型 */
  type: 'error' | 'warning' | 'info';
  /** 验证消息 */
  message: string;
  /** 字段名 */
  field?: string;
}

/** 生命周期状态 */
export type LifecycleStatus = 'draft' | 'active' | 'inactive' | 'deprecated';

// ============================================================================
// 可视化编辑器相关类型
// ============================================================================

/** 可视化节点类型 */
export type VisualNodeType = 'product' | 'assembly' | 'component' | 'parameter';

/** 可视化节点定义 */
export interface VisualNode {
  /** 节点ID */
  id: string;
  /** 节点类型 */
  type: VisualNodeType;
  /** 节点名称 */
  name: string;
  /** 节点标签 */
  label: string;
  /** 节点描述 */
  description?: string;
  /** 节点图标 */
  icon?: string;
  /** 节点颜色 */
  color?: string;
  /** 节点大小 */
  size?: number;
  /** 节点位置 */
  position: {
    x: number;
    y: number;
  };
  /** 是否可选 */
  optional?: boolean;
  /** 节点状态 */
  status: 'active' | 'inactive' | 'error' | 'warning';
  /** 关联的数据对象 */
  data: any;
  /** 子节点ID列表 */
  children?: string[];
  /** 父节点ID */
  parent?: string;
  /** 节点层级 */
  level: number;
  /** 是否展开 */
  expanded?: boolean;
}

/** 可视化连接线定义 */
export interface VisualEdge {
  /** 连接线ID */
  id: string;
  /** 源节点ID */
  source: string;
  /** 目标节点ID */
  target: string;
  /** 连接线类型 */
  type: 'hierarchy' | 'dependency' | 'constraint' | 'parameter';
  /** 连接线标签 */
  label?: string;
  /** 连接线样式 */
  style?: {
    color?: string;
    width?: number;
    dashArray?: string;
  };
  /** 关联数据 */
  data?: any;
}

/** 可视化布局配置 */
export interface VisualLayoutConfig {
  /** 布局类型 */
  type: 'tree' | 'force' | 'circular' | 'grid';
  /** 节点间距 */
  nodeSpacing: {
    horizontal: number;
    vertical: number;
  };
  /** 自动布局 */
  autoLayout: boolean;
  /** 动画配置 */
  animation: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
}

/** 可视化视图状态 */
export interface VisualViewState {
  /** 缩放级别 */
  zoom: number;
  /** 视图中心点 */
  center: {
    x: number;
    y: number;
  };
  /** 选中的节点ID列表 */
  selectedNodes: string[];
  /** 高亮的节点ID列表 */
  highlightedNodes: string[];
  /** 当前编辑的节点ID */
  editingNode?: string;
}

/** 可视化操作历史 */
export interface VisualOperation {
  /** 操作ID */
  id: string;
  /** 操作类型 */
  type: 'add_node' | 'remove_node' | 'move_node' | 'edit_node' | 'add_edge' | 'remove_edge';
  /** 操作时间 */
  timestamp: string;
  /** 操作数据 */
  data: any;
  /** 逆向操作数据 */
  reverseData?: any;
}