<script setup lang="ts">
import OrderList from '@/components/scheduling/OrderList.vue';
import ProductionKanban from '@/components/scheduling/ProductionKanban.vue';
</script>

<template>
  <div class="p-4 sm:p-6 lg:p-8">
    <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-50 mb-6">
      智能排产工作台
    </h1>
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
      <div class="lg:col-span-3">
        <!-- 待处理订单区域 -->
        <OrderList />
      </div>
      <div class="lg:col-span-3">
        <!-- 生产计划看板 -->
        <ProductionKanban />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for the workbench view here */
</style>
