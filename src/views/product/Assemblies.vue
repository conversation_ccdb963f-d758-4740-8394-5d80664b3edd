<template>
  <div class="assemblies-management">

    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">构件管理</h1>
        <p class="text-gray-600 mt-1">管理产品结构中的构件定义和装配工艺</p>
      </div>
      <div class="flex items-center gap-3">
        <Button @click="showStatistics = !showStatistics" variant="outline">
          <BarChart3 class="w-4 h-4 mr-2" />
          统计信息
        </Button>
        <Button @click="openCreateDialog" class="bg-blue-600 hover:bg-blue-700">
          <Plus class="w-4 h-4 mr-2" />
          新建构件
        </Button>
      </div>
    </div>

    <!-- 统计信息面板 -->
    <div v-if="showStatistics && statistics" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">总构件数</p>
              <p class="text-2xl font-bold">{{ statistics.total }}</p>
            </div>
            <Layers class="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">框架构件</p>
              <p class="text-2xl font-bold">{{ statistics.byType.frame_assembly || 0 }}</p>
            </div>
            <Square class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">玻璃构件</p>
              <p class="text-2xl font-bold">{{ statistics.byType.glass_assembly || 0 }}</p>
            </div>
            <Package class="w-8 h-8 text-purple-500" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">最近创建</p>
              <p class="text-2xl font-bold">{{ statistics.recentlyCreated }}</p>
            </div>
            <Clock class="w-8 h-8 text-orange-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和操作栏 -->
    <div class="bg-white rounded-lg border p-4 mb-6">
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- 搜索框 -->
        <div class="flex-1">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              v-model="searchQuery"
              placeholder="搜索构件名称、编码或标签..."
              class="pl-10"
              @input="handleSearch"
            />
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="flex items-center gap-2">
          <!-- 构件类型筛选 -->
          <Select v-model="selectedType">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="构件类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="frame_assembly">框架构件</SelectItem>
              <SelectItem value="glass_assembly">玻璃构件</SelectItem>
              <SelectItem value="hardware_assembly">五金构件</SelectItem>
              <SelectItem value="complete_assembly">完整构件</SelectItem>
            </SelectContent>
          </Select>

          <!-- 状态筛选 -->
          <Select v-model="selectedStatus">
            <SelectTrigger class="w-32">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="draft">草稿</SelectItem>
              <SelectItem value="active">激活</SelectItem>
              <SelectItem value="deprecated">已弃用</SelectItem>
              <SelectItem value="archived">已归档</SelectItem>
            </SelectContent>
          </Select>

          <!-- 视图切换 -->
          <div class="flex items-center border rounded-md">
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'bg-gray-100': viewMode === 'list' }"
              @click="viewMode = 'list'"
            >
              <List class="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'bg-gray-100': viewMode === 'grid' }"
              @click="viewMode = 'grid'"
            >
              <Grid3X3 class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedAssemblies.length > 0" class="flex items-center justify-between mt-4 pt-4 border-t">
        <div class="flex items-center gap-2">
          <Checkbox
            :checked="selectedAssemblies.length === paginatedAssemblies.length"
            @update:checked="toggleSelectAll"
          />
          <span class="text-sm text-gray-600">
            已选择 {{ selectedAssemblies.length }} 个构件
          </span>
        </div>
        <div class="flex items-center gap-2">
          <Button @click="showBatchActions = true" variant="outline" size="sm">
            批量操作
          </Button>
          <Button @click="selectedAssemblies = []" variant="ghost" size="sm">
            取消选择
          </Button>
        </div>
      </div>
    </div>

    <!-- 构件列表 -->
    <div class="bg-white rounded-lg border">
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="flex items-center gap-2 text-gray-500">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span>加载中...</span>
        </div>
      </div>

      <div v-else-if="paginatedAssemblies.length === 0" class="text-center py-12">
        <Layers class="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无构件</h3>
        <p class="text-gray-500 mb-4">开始创建您的第一个构件</p>
        <Button @click="openCreateDialog" class="bg-blue-600 hover:bg-blue-700">
          <Plus class="w-4 h-4 mr-2" />
          新建构件
        </Button>
      </div>

      <div v-else>
        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="divide-y">
          <AssemblyListItem
            v-for="assembly in paginatedAssemblies"
            :key="assembly.id"
            :assembly="assembly"
            :selected="selectedAssemblies.includes(assembly.id)"
            @select="toggleAssemblySelection"
            @edit="editAssembly"
            @duplicate="duplicateAssembly"
            @delete="deleteAssembly"
            @design="openDesigner"
          />
        </div>

        <!-- 卡片视图 -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
          <AssemblyCard
            v-for="assembly in paginatedAssemblies"
            :key="assembly.id"
            :assembly="assembly"
            :selected="selectedAssemblies.includes(assembly.id)"
            @select="toggleAssemblySelection"
            @edit="editAssembly"
            @duplicate="duplicateAssembly"
            @delete="deleteAssembly"
            @design="openDesigner"
          />
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex items-center justify-center p-4 border-t">
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="currentPage === 1"
            @click="currentPage = currentPage - 1"
          >
            <ChevronLeft class="w-4 h-4" />
          </Button>
          
          <span class="text-sm text-gray-600">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          
          <Button
            variant="outline"
            size="sm"
            :disabled="currentPage === totalPages"
            @click="currentPage = currentPage + 1"
          >
            <ChevronRight class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 构件编辑器对话框 -->
    <AssemblyEditor
      :open="showEditor"
      :assembly="editingAssembly"
      @update:open="showEditor = $event"
      @save="handleSaveAssembly"
    />

    <!-- 构件设计器对话框 -->
    <AssemblyDesigner
      :open="showDesigner"
      :assembly="designingAssembly"
      @update:open="showDesigner = $event"
      @save="handleSaveAssembly"
    />

    <!-- 批量操作对话框 -->
    <BatchOperationDialog
      :open="showBatchActions"
      :operation-type="'delete'"
      :selected-count="selectedAssemblies.length"
      @update:open="showBatchActions = $event"
      @confirm="handleBatchComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  Plus,
  Search,
  List,
  Grid3X3,
  BarChart3,
  Layers,
  Square,
  Package,
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import AssemblyListItem from '@/components/product/AssemblyListItem.vue';
import AssemblyCard from '@/components/product/AssemblyCard.vue';
import AssemblyEditor from '@/components/product/AssemblyEditor.vue';
import AssemblyDesigner from '@/components/product/AssemblyDesigner.vue';
import BatchOperationDialog from '@/components/product/BatchOperationDialog.vue';

import { assemblyService } from '@/services/assemblyService';
import type { Assembly, AssemblyStatistics } from '@/services/assemblyService';

// 响应式数据
const loading = ref(false);
const assemblies = ref<Assembly[]>([]);
const searchQuery = ref('');
const selectedType = ref('all');
const selectedStatus = ref('all');
const selectedAssemblies = ref<string[]>([]);
const viewMode = ref<'list' | 'grid'>('list');
const currentPage = ref(1);
const pageSize = ref(20);

// 对话框状态
const showEditor = ref(false);
const showDesigner = ref(false);
const showBatchActions = ref(false);
const showStatistics = ref(false);
const editingAssembly = ref<Assembly | null>(null);
const designingAssembly = ref<Assembly | null>(null);

// 统计信息
const statistics = ref<AssemblyStatistics | null>(null);

// 计算属性
const filteredAssemblies = computed(() => {
  return assemblies.value.filter(assembly => {
    // 搜索筛选
    if (searchQuery.value) {
      const search = searchQuery.value.toLowerCase();
      const matchesSearch =
        assembly.name.toLowerCase().includes(search) ||
        assembly.code.toLowerCase().includes(search) ||
        (assembly.description && assembly.description.toLowerCase().includes(search)) ||
        assembly.tags.some(tag => tag.toLowerCase().includes(search));

      if (!matchesSearch) return false;
    }

    // 类型筛选
    if (selectedType.value && selectedType.value !== 'all' && assembly.assemblyType !== selectedType.value) {
      return false;
    }

    // 状态筛选
    if (selectedStatus.value && selectedStatus.value !== 'all' && assembly.status !== selectedStatus.value) {
      return false;
    }

    return true;
  });
});

const totalPages = computed(() => Math.ceil(filteredAssemblies.value.length / pageSize.value));

const paginatedAssemblies = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredAssemblies.value.slice(start, end);
});

// 方法
const loadAssemblies = async () => {
  loading.value = true;
  try {
    const result = await assemblyService.getAssemblies();
    assemblies.value = result.assemblies;
  } catch (error) {
    console.error('加载构件失败:', error);
  } finally {
    loading.value = false;
  }
};

const loadStatistics = async () => {
  try {
    statistics.value = await assemblyService.getStatistics();
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const openCreateDialog = () => {
  editingAssembly.value = null;
  showEditor.value = true;
};

const editAssembly = (assembly: Assembly) => {
  editingAssembly.value = assembly;
  showEditor.value = true;
};

const openDesigner = (assembly: Assembly) => {
  designingAssembly.value = assembly;
  showDesigner.value = true;
};

const duplicateAssembly = async (assembly: Assembly) => {
  try {
    await assemblyService.duplicateAssembly(assembly.id, `${assembly.name}_副本`);
    await loadAssemblies();
  } catch (error) {
    console.error('复制构件失败:', error);
  }
};

const deleteAssembly = async (assembly: Assembly) => {
  if (confirm(`确定要删除构件"${assembly.name}"吗？`)) {
    try {
      await assemblyService.deleteAssembly(assembly.id);
      await loadAssemblies();
    } catch (error) {
      console.error('删除构件失败:', error);
    }
  }
};

const handleSaveAssembly = async (assemblyData: any) => {
  try {
    if (editingAssembly.value) {
      await assemblyService.updateAssembly(editingAssembly.value.id, assemblyData);
    } else {
      await assemblyService.createAssembly(assemblyData);
    }
    await loadAssemblies();
    showEditor.value = false;
    showDesigner.value = false;
    editingAssembly.value = null;
    designingAssembly.value = null;
  } catch (error) {
    console.error('保存构件失败:', error);
  }
};

const toggleAssemblySelection = (assemblyId: string) => {
  const index = selectedAssemblies.value.indexOf(assemblyId);
  if (index > -1) {
    selectedAssemblies.value.splice(index, 1);
  } else {
    selectedAssemblies.value.push(assemblyId);
  }
};

const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedAssemblies.value = paginatedAssemblies.value.map(a => a.id);
  } else {
    selectedAssemblies.value = [];
  }
};

const handleBatchComplete = async () => {
  await loadAssemblies();
  selectedAssemblies.value = [];
  showBatchActions.value = false;
};

// 监听筛选条件变化
watch([selectedType, selectedStatus], () => {
  currentPage.value = 1;
});

// 组件挂载时加载数据
onMounted(() => {
  loadAssemblies();
  loadStatistics();
});
</script>
