<template>
  <div class="component-management-view h-full flex flex-col">
    <!-- 头部工具栏 -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">组件库管理</h1>
          <p class="text-sm text-gray-600 mt-1">管理产品结构中的组件定义、参数、约束和工艺要求</p>
        </div>
        <div class="flex items-center gap-3">
          <Button @click="showStatistics" variant="outline" size="sm">
            <BarChart3 class="w-4 h-4 mr-2" />
            统计信息
          </Button>
          <Button @click="openComponentEditor" class="bg-blue-600 hover:bg-blue-700">
            <Plus class="w-4 h-4 mr-2" />
            新建组件
          </Button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧筛选面板 -->
      <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <h3 class="text-sm font-medium text-gray-900 mb-3">筛选条件</h3>
          
          <!-- 搜索框 -->
          <div class="mb-4">
            <div class="relative">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                v-model="searchQuery"
                placeholder="搜索组件名称或编码..."
                class="pl-10"
                @input="handleSearch"
              />
            </div>
          </div>

          <!-- 组件类型筛选 -->
          <div class="mb-4">
            <Label class="text-sm font-medium text-gray-700 mb-2 block">组件类型</Label>
            <div class="space-y-2">
              <label v-for="type in componentTypes" :key="type.value" class="flex items-center">
                <Checkbox
                  :checked="selectedTypes.includes(type.value)"
                  @update:checked="toggleType(type.value)"
                />
                <span class="ml-2 text-sm text-gray-700">{{ type.label }}</span>
                <Badge variant="secondary" class="ml-auto text-xs">
                  {{ getTypeCount(type.value) }}
                </Badge>
              </label>
            </div>
          </div>

          <!-- 状态筛选 -->
          <div class="mb-4">
            <Label class="text-sm font-medium text-gray-700 mb-2 block">状态</Label>
            <div class="space-y-2">
              <label v-for="status in componentStatuses" :key="status.value" class="flex items-center">
                <Checkbox
                  :checked="selectedStatuses.includes(status.value)"
                  @update:checked="toggleStatus(status.value)"
                />
                <span class="ml-2 text-sm text-gray-700">{{ status.label }}</span>
                <Badge variant="secondary" class="ml-auto text-xs">
                  {{ getStatusCount(status.value) }}
                </Badge>
              </label>
            </div>
          </div>

          <!-- 标签筛选 -->
          <div class="mb-4">
            <Label class="text-sm font-medium text-gray-700 mb-2 block">标签</Label>
            <div class="flex flex-wrap gap-1">
              <Badge
                v-for="tag in availableTags"
                :key="tag"
                :variant="selectedTags.includes(tag) ? 'default' : 'outline'"
                class="cursor-pointer text-xs"
                @click="toggleTag(tag)"
              >
                {{ tag }}
              </Badge>
            </div>
          </div>
        </div>

        <!-- 筛选结果统计 -->
        <div class="p-4 bg-gray-50">
          <div class="text-sm text-gray-600">
            显示 {{ filteredComponents.length }} / {{ components.length }} 个组件
          </div>
        </div>
      </div>

      <!-- 右侧组件列表和详情 -->
      <div class="flex-1 flex flex-col">
        <!-- 工具栏 -->
        <div class="bg-white border-b border-gray-200 px-6 py-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <span class="text-sm text-gray-600">排序:</span>
                <Select v-model="sortBy">
                  <SelectTrigger class="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">名称</SelectItem>
                    <SelectItem value="code">编码</SelectItem>
                    <SelectItem value="type">类型</SelectItem>
                    <SelectItem value="updatedAt">更新时间</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div class="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  :class="{ 'bg-gray-100': viewMode === 'list' }"
                  @click="viewMode = 'list'"
                >
                  <List class="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  :class="{ 'bg-gray-100': viewMode === 'grid' }"
                  @click="viewMode = 'grid'"
                >
                  <Grid3X3 class="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div class="flex items-center gap-2">
              <Button
                v-if="selectedComponents.length > 0"
                variant="outline"
                size="sm"
                @click="showBatchActions = true"
              >
                <Settings class="w-4 h-4 mr-2" />
                批量操作 ({{ selectedComponents.length }})
              </Button>
            </div>
          </div>
        </div>

        <!-- 组件列表 -->
        <div class="flex-1 overflow-auto p-6">
          <div v-if="loading" class="flex items-center justify-center h-64">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p class="text-gray-600">加载组件数据...</p>
            </div>
          </div>

          <div v-else-if="filteredComponents.length === 0" class="text-center py-12">
            <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无组件</h3>
            <p class="text-gray-600 mb-4">
              {{ components.length === 0 ? '还没有创建任何组件' : '没有符合筛选条件的组件' }}
            </p>
            <Button @click="openComponentEditor" class="bg-blue-600 hover:bg-blue-700">
              <Plus class="w-4 h-4 mr-2" />
              创建第一个组件
            </Button>
          </div>

          <!-- 列表视图 -->
          <div v-else-if="viewMode === 'list'" class="space-y-4">
            <div
              v-for="component in paginatedComponents"
              :key="component.id"
              class="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
            >
              <div class="p-4">
                <div class="flex items-start justify-between">
                  <div class="flex items-start gap-3">
                    <Checkbox
                      :checked="selectedComponents.includes(component.id)"
                      @update:checked="toggleComponentSelection(component.id)"
                    />
                    <div class="flex-1">
                      <div class="flex items-center gap-3 mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">{{ component.name }}</h3>
                        <Badge :variant="getTypeVariant(component.componentType)">
                          {{ getTypeLabel(component.componentType) }}
                        </Badge>
                        <Badge :variant="getStatusVariant(component.status)">
                          {{ getStatusLabel(component.status) }}
                        </Badge>
                      </div>
                      <p class="text-sm text-gray-600 mb-2">{{ component.code }}</p>
                      <p class="text-sm text-gray-500 mb-3">{{ component.description || '暂无描述' }}</p>
                      
                      <div class="flex items-center gap-4 text-sm text-gray-600">
                        <span>参数: {{ component.parameters?.length || 0 }}</span>
                        <span>约束: {{ component.constraints?.length || 0 }}</span>
                        <span>工艺: {{ component.processRequirements?.length || 0 }}</span>
                        <span>更新: {{ formatDate(component.updatedAt) }}</span>
                      </div>
                      
                      <div v-if="component.tags && component.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
                        <Badge
                          v-for="tag in component.tags"
                          :key="tag"
                          variant="outline"
                          class="text-xs"
                        >
                          {{ tag }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center gap-2">
                    <Button variant="ghost" size="sm" @click="viewComponent(component)">
                      <Eye class="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="editComponent(component)">
                      <Edit class="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="duplicateComponent(component)">
                      <Copy class="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="deleteComponent(component)" class="text-red-600">
                      <Trash2 class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 网格视图 -->
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div
              v-for="component in paginatedComponents"
              :key="component.id"
              class="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors p-4"
            >
              <div class="flex items-start justify-between mb-3">
                <Checkbox
                  :checked="selectedComponents.includes(component.id)"
                  @update:checked="toggleComponentSelection(component.id)"
                />
                <div class="flex items-center gap-1">
                  <Button variant="ghost" size="sm" @click="viewComponent(component)">
                    <Eye class="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" @click="editComponent(component)">
                    <Edit class="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div class="mb-3">
                <h3 class="font-semibold text-gray-900 mb-1">{{ component.name }}</h3>
                <p class="text-sm text-gray-600 mb-2">{{ component.code }}</p>
                <div class="flex items-center gap-2 mb-2">
                  <Badge :variant="getTypeVariant(component.componentType)" class="text-xs">
                    {{ getTypeLabel(component.componentType) }}
                  </Badge>
                  <Badge :variant="getStatusVariant(component.status)" class="text-xs">
                    {{ getStatusLabel(component.status) }}
                  </Badge>
                </div>
              </div>
              
              <div class="text-xs text-gray-500 space-y-1">
                <div class="flex justify-between">
                  <span>参数:</span>
                  <span>{{ component.parameters?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>约束:</span>
                  <span>{{ component.constraints?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>工艺:</span>
                  <span>{{ component.processRequirements?.length || 0 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="totalPages > 1" class="flex items-center justify-center mt-8">
            <div class="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === 1"
                @click="currentPage = currentPage - 1"
              >
                <ChevronLeft class="w-4 h-4" />
              </Button>
              
              <span class="text-sm text-gray-600">
                第 {{ currentPage }} 页，共 {{ totalPages }} 页
              </span>
              
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === totalPages"
                @click="currentPage = currentPage + 1"
              >
                <ChevronRight class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件编辑器对话框 -->
    <ComponentEditor
      :open="showEditor"
      :component="editingComponent"
      @update:open="showEditor = $event"
      @save="handleSaveComponent"
    />

    <!-- 组件详情对话框 -->
    <Dialog v-model:open="showDetails">
      <DialogContent class="w-[90vw] h-[90vh] max-w-none max-h-none overflow-hidden flex flex-col">
        <DialogHeader class="border-b border-gray-200 pb-4">
          <DialogTitle class="text-xl">组件详情</DialogTitle>
          <DialogDescription v-if="viewingComponent">
            {{ viewingComponent.name }} ({{ viewingComponent.code }})
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-auto p-6">
          <ComponentPreview
            v-if="viewingComponent"
            :component="viewingComponent"
          />
        </div>
      </DialogContent>
    </Dialog>

    <!-- 批量操作对话框 -->
    <Dialog v-model:open="showBatchActions">
      <DialogContent class="w-[80vw] h-[80vh] max-w-none max-h-none overflow-hidden flex flex-col">
        <DialogHeader class="border-b border-gray-200 pb-4">
          <DialogTitle>批量操作</DialogTitle>
          <DialogDescription>
            已选择 {{ selectedComponents.length }} 个组件
          </DialogDescription>
        </DialogHeader>
        <div class="flex-1 overflow-auto p-6">
          <BatchOperationDialog
            v-if="showBatchActions"
            :selected-components="selectedComponents"
            @complete="handleBatchComplete"
            @cancel="showBatchActions = false"
          />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { 
  Plus, Search, List, Grid3X3, Eye, Edit, Copy, Trash2, Package, 
  Settings, BarChart3, ChevronLeft, ChevronRight 
} from 'lucide-vue-next';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

import ComponentEditor from '@/components/product/ComponentEditor.vue';
import ComponentPreview from '@/components/product/ComponentPreview.vue';
import BatchOperationDialog from '@/components/product/BatchOperationDialog.vue';

import { componentService } from '@/services/componentService';
import type { Component } from '@/types/product-structure';

// 响应式数据
const loading = ref(false);
const components = ref<Component[]>([]);
const searchQuery = ref('');
const selectedTypes = ref<string[]>([]);
const selectedStatuses = ref<string[]>([]);
const selectedTags = ref<string[]>([]);
const selectedComponents = ref<string[]>([]);
const sortBy = ref('name');
const viewMode = ref<'list' | 'grid'>('list');
const currentPage = ref(1);
const pageSize = ref(20);

// 对话框状态
const showEditor = ref(false);
const showDetails = ref(false);
const showBatchActions = ref(false);
const editingComponent = ref<Component | null>(null);
const viewingComponent = ref<Component | null>(null);

// 筛选选项
const componentTypes = [
  { value: 'frame', label: '框料' },
  { value: 'glass', label: '玻璃' },
  { value: 'hardware', label: '五金' },
  { value: 'seal', label: '密封' },
  { value: 'other', label: '其他' }
];

const componentStatuses = [
  { value: 'draft', label: '草稿' },
  { value: 'active', label: '活跃' },
  { value: 'deprecated', label: '已弃用' },
  { value: 'archived', label: '已归档' }
];

// 计算属性
const availableTags = computed(() => {
  const tags = new Set<string>();
  components.value.forEach(component => {
    component.tags?.forEach(tag => tags.add(tag));
  });
  return Array.from(tags).sort();
});

const filteredComponents = computed(() => {
  let filtered = components.value;

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(component =>
      component.name.toLowerCase().includes(query) ||
      component.code.toLowerCase().includes(query) ||
      component.description?.toLowerCase().includes(query)
    );
  }

  // 类型筛选
  if (selectedTypes.value.length > 0) {
    filtered = filtered.filter(component =>
      selectedTypes.value.includes(component.componentType)
    );
  }

  // 状态筛选
  if (selectedStatuses.value.length > 0) {
    filtered = filtered.filter(component =>
      selectedStatuses.value.includes(component.status)
    );
  }

  // 标签筛选
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(component =>
      component.tags?.some(tag => selectedTags.value.includes(tag))
    );
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'code':
        return a.code.localeCompare(b.code);
      case 'type':
        return a.componentType.localeCompare(b.componentType);
      case 'updatedAt':
        return new Date(b.updatedAt || 0).getTime() - new Date(a.updatedAt || 0).getTime();
      default:
        return 0;
    }
  });

  return filtered;
});

const totalPages = computed(() => Math.ceil(filteredComponents.value.length / pageSize.value));

const paginatedComponents = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredComponents.value.slice(start, end);
});

// 方法
const loadComponents = async () => {
  loading.value = true;
  try {
    const result = await componentService.getComponents();
    components.value = result.components;
  } catch (error) {
    console.error('加载组件失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const toggleType = (type: string) => {
  const index = selectedTypes.value.indexOf(type);
  if (index > -1) {
    selectedTypes.value.splice(index, 1);
  } else {
    selectedTypes.value.push(type);
  }
  currentPage.value = 1;
};

const toggleStatus = (status: string) => {
  const index = selectedStatuses.value.indexOf(status);
  if (index > -1) {
    selectedStatuses.value.splice(index, 1);
  } else {
    selectedStatuses.value.push(status);
  }
  currentPage.value = 1;
};

const toggleTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  } else {
    selectedTags.value.push(tag);
  }
  currentPage.value = 1;
};

const toggleComponentSelection = (componentId: string) => {
  const index = selectedComponents.value.indexOf(componentId);
  if (index > -1) {
    selectedComponents.value.splice(index, 1);
  } else {
    selectedComponents.value.push(componentId);
  }
};

const getTypeCount = (type: string) => {
  return components.value.filter(c => c.componentType === type).length;
};

const getStatusCount = (status: string) => {
  return components.value.filter(c => c.status === status).length;
};

const getTypeLabel = (type: string) => {
  return componentTypes.find(t => t.value === type)?.label || type;
};

const getStatusLabel = (status: string) => {
  return componentStatuses.find(s => s.value === status)?.label || status;
};

const getTypeVariant = (type: string) => {
  const variants: Record<string, any> = {
    frame: 'default',
    glass: 'secondary',
    hardware: 'outline',
    seal: 'destructive',
    other: 'outline'
  };
  return variants[type] || 'outline';
};

const getStatusVariant = (status: string) => {
  const variants: Record<string, any> = {
    draft: 'secondary',
    active: 'default',
    deprecated: 'destructive',
    archived: 'outline'
  };
  return variants[status] || 'outline';
};

const formatDate = (date: string | Date | undefined) => {
  if (!date) return '未知';
  return new Date(date).toLocaleDateString('zh-CN');
};

const openComponentEditor = () => {
  editingComponent.value = null;
  showEditor.value = true;
};

const editComponent = (component: Component) => {
  editingComponent.value = component;
  showEditor.value = true;
};

const viewComponent = (component: Component) => {
  viewingComponent.value = component;
  showDetails.value = true;
};

const duplicateComponent = async (component: Component) => {
  try {
    await componentService.duplicateComponent(component.id, `${component.name}_副本`);
    await loadComponents();
  } catch (error) {
    console.error('复制组件失败:', error);
  }
};

const deleteComponent = async (component: Component) => {
  if (confirm(`确定要删除组件"${component.name}"吗？`)) {
    try {
      await componentService.deleteComponent(component.id);
      await loadComponents();
    } catch (error) {
      console.error('删除组件失败:', error);
    }
  }
};

const handleSaveComponent = async (componentData: any) => {
  try {
    if (editingComponent.value) {
      await componentService.updateComponent(editingComponent.value.id, componentData);
    } else {
      await componentService.createComponent(componentData);
    }
    await loadComponents();
    showEditor.value = false;
    editingComponent.value = null;
  } catch (error) {
    console.error('保存组件失败:', error);
  }
};

const handleBatchComplete = async () => {
  await loadComponents();
  selectedComponents.value = [];
  showBatchActions.value = false;
};

const showStatistics = async () => {
  try {
    const stats = await componentService.getStatistics();
    alert(`组件统计信息：
总数: ${stats.total}
框料: ${stats.byType.frame || 0}
玻璃: ${stats.byType.glass || 0}
五金: ${stats.byType.hardware || 0}
密封: ${stats.byType.seal || 0}
其他: ${stats.byType.other || 0}

活跃: ${stats.byStatus.active || 0}
草稿: ${stats.byStatus.draft || 0}
已弃用: ${stats.byStatus.deprecated || 0}
已归档: ${stats.byStatus.archived || 0}`);
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
};

// 监听器
watch([selectedTypes, selectedStatuses, selectedTags, sortBy], () => {
  currentPage.value = 1;
});

// 创建示例数据
const createSampleData = async () => {
  const sampleComponents = [
    {
      name: '铝合金窗框',
      code: 'ALU_FRAME_001',
      description: '标准铝合金窗框，支持多种尺寸配置',
      componentType: 'frame' as const,
      materialCategoryId: 'aluminum-frame',
      materialCategoryName: '铝合金框料',
      materialCategoryCode: 'ALF001',
      parameters: [
        {
          id: 'param-width',
          name: 'width',
          displayName: '宽度',
          type: 'number' as const,
          unit: 'mm',
          defaultValue: 1200,
          minValue: 600,
          maxValue: 2400,
          required: true,
          visible: true,
          editable: true,
          category: 'dimension' as const
        },
        {
          id: 'param-height',
          name: 'height',
          displayName: '高度',
          type: 'number' as const,
          unit: 'mm',
          defaultValue: 1500,
          minValue: 800,
          maxValue: 2200,
          required: true,
          visible: true,
          editable: true,
          category: 'dimension' as const
        }
      ],
      constraints: [
        {
          id: 'constraint-aspect-ratio',
          name: '宽高比约束',
          type: 'dimension' as const,
          expression: 'width / height >= 0.4 && width / height <= 4.0',
          errorMessage: '宽高比必须在0.4到4.0之间',
          severity: 'error' as const,
          enabled: true
        }
      ],
      tags: ['铝合金', '窗框', '标准件'],
      status: 'active' as const,
      reusable: true
    },
    {
      name: '钢化玻璃',
      code: 'GLASS_001',
      description: '6mm钢化玻璃，安全性能优异',
      componentType: 'glass' as const,
      materialCategoryId: 'tempered-glass',
      materialCategoryName: '钢化玻璃',
      materialCategoryCode: 'TG001',
      parameters: [
        {
          id: 'param-thickness',
          name: 'thickness',
          displayName: '厚度',
          type: 'select' as const,
          options: [
            { value: '4', label: '4mm' },
            { value: '5', label: '5mm' },
            { value: '6', label: '6mm' },
            { value: '8', label: '8mm' }
          ],
          defaultValue: '6',
          required: true,
          visible: true,
          editable: true,
          category: 'material' as const
        }
      ],
      tags: ['玻璃', '钢化', '安全'],
      status: 'active' as const,
      reusable: true
    },
    {
      name: '门锁五金',
      code: 'HARDWARE_001',
      description: '高品质门锁五金套装',
      componentType: 'hardware' as const,
      materialCategoryId: 'door-hardware',
      materialCategoryName: '门五金',
      materialCategoryCode: 'DH001',
      parameters: [
        {
          id: 'param-material',
          name: 'material',
          displayName: '材质',
          type: 'select' as const,
          options: [
            { value: 'stainless', label: '不锈钢' },
            { value: 'aluminum', label: '铝合金' },
            { value: 'zinc', label: '锌合金' }
          ],
          defaultValue: 'stainless',
          required: true,
          visible: true,
          editable: true,
          category: 'material' as const
        }
      ],
      tags: ['五金', '门锁', '不锈钢'],
      status: 'draft' as const,
      reusable: false
    },
    {
      name: '橡胶密封条',
      code: 'SEAL_001',
      description: 'EPDM橡胶密封条，防水防尘',
      componentType: 'seal' as const,
      materialCategoryId: 'rubber-seal',
      materialCategoryName: '橡胶密封',
      materialCategoryCode: 'RS001',
      parameters: [
        {
          id: 'param-hardness',
          name: 'hardness',
          displayName: '硬度',
          type: 'number' as const,
          unit: 'Shore A',
          defaultValue: 65,
          minValue: 50,
          maxValue: 80,
          required: true,
          visible: true,
          editable: true,
          category: 'material' as const
        }
      ],
      tags: ['密封', '橡胶', '防水'],
      status: 'active' as const,
      reusable: true
    }
  ];

  try {
    for (const componentData of sampleComponents) {
      await componentService.createComponent(componentData);
    }
  } catch (error) {
    console.error('创建示例数据失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  await loadComponents();

  // 如果没有组件数据，创建示例数据
  if (components.value.length === 0) {
    await createSampleData();
    await loadComponents();
  }
});
</script>

<style scoped>
.component-management-view {
  background-color: #f9fafb;
}
</style>
