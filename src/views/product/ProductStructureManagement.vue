<template>
  <div class="product-structure-management">
    <!-- 页面标题和操作栏 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">产品结构管理</h1>
        <p class="text-gray-600 mt-1">管理产品结构定义，支持多维度筛选、版本控制和结构验证</p>
      </div>
      <div class="flex gap-3">
        <!-- 视图切换 -->
        <div class="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
          <Button
            :variant="viewMode === 'list' ? 'default' : 'ghost'"
            size="sm"
            @click="viewMode = 'list'"
          >
            <List class="w-4 h-4 mr-1" />
            列表视图
          </Button>
          <Button
            :variant="viewMode === 'visual' ? 'default' : 'ghost'"
            size="sm"
            @click="viewMode = 'visual'"
          >
            <Network class="w-4 h-4 mr-1" />
            可视化设计
          </Button>
        </div>

        <div class="w-px h-6 bg-gray-300"></div>

        <Button variant="outline" @click="exportStructures">
          <Download class="w-4 h-4 mr-2" />
          导出结构
        </Button>
        <Button variant="outline" @click="validateAllStructures">
          <CheckCircle class="w-4 h-4 mr-2" />
          批量验证
        </Button>
        <Button variant="outline" @click="testDataLoad" class="border-blue-300 text-blue-700 hover:bg-blue-50">
          <CheckCircle class="w-4 h-4 mr-2" />
          测试数据加载
        </Button>
        <Button @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新建结构
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Package class="w-5 h-5 text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">总结构数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalStructures }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <TrendingUp class="w-5 h-5 text-green-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">活跃结构</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeStructures }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <BarChart3 class="w-5 h-5 text-purple-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">本月变更</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.monthlyChanges }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <AlertTriangle class="w-5 h-5 text-orange-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">验证警告</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.validationWarnings }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 搜索和筛选 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <ProductStructureFilters
          v-model:filters="filters"
          v-model:search="searchKeyword"
          :result-count="filteredStructures.length"
          @search="handleSearch"
          @filter="handleFilter"
          @reset="handleResetFilters"
        />
      </CardContent>
    </Card>

    <!-- 列表视图 -->
    <Card v-if="viewMode === 'list'">
      <CardContent class="p-0">
        <ProductStructureTable
          :structures="filteredStructures"
          :loading="loading"
          :pagination="pagination"
          @edit="handleEdit"
          @delete="handleDelete"
          @view-details="handleViewDetails"
          @view-versions="handleViewVersions"
          @duplicate="handleDuplicate"
          @validate="handleValidateStructure"
          @design="handleDesign"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </CardContent>
    </Card>

    <!-- 可视化设计视图 -->
    <Card v-else-if="viewMode === 'visual'" class="h-[calc(100vh-300px)]">
      <CardContent class="p-0 h-full">
        <div v-if="selectedStructureForVisual">
          <ProductStructureVisualEditor
            :structure="selectedStructureForVisual"
            @save="handleVisualSave"
            @validate="handleVisualValidate"
            @update:structure="handleVisualUpdate"
          />
        </div>
        <div v-else class="h-full flex items-center justify-center">
          <div class="text-center">
            <Network class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择产品结构进行可视化设计</h3>
            <p class="text-gray-600 mb-4">从下方列表中选择一个产品结构开始可视化设计</p>
            <div class="max-h-40 overflow-y-auto border rounded-lg">
              <div
                v-for="structure in filteredStructures"
                :key="structure.id"
                class="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 flex items-center justify-between"
                @click="selectStructureForVisual(structure)"
              >
                <div>
                  <div class="font-medium">{{ structure.name }}</div>
                  <div class="text-sm text-gray-600">{{ structure.code }}</div>
                </div>
                <Badge :variant="getStatusVariant(structure.status)">
                  {{ getStatusText(structure.status) }}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 创建/编辑对话框 -->
    <ProductStructureDialog
      v-model:open="showCreateDialog"
      :structure="editingStructure"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- 详情对话框 -->
    <ProductStructureDetailsDialog
      v-model:open="showDetailsDialog"
      :structure="selectedStructure"
    />

    <!-- 版本历史对话框 -->
    <ProductStructureVersionDialog
      v-model:open="showVersionDialog"
      :structure="selectedStructure"
      @restore-version="handleRestoreVersion"
    />

    <!-- 验证结果对话框 -->
    <ProductStructureValidationDialog
      v-model:open="showValidationDialog"
      :validation-result="validationResult"
      :structure="selectedStructure"
      @apply-fix="handleApplyFix"
    />

    <!-- 可视化设计对话框 -->
    <ProductStructureDesignDialog
      v-model:open="showDesignDialog"
      :structure="designStructure"
      @save="handleDesignSave"
      @validate="handleDesignValidate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Package,
  Plus,
  Download,
  TrendingUp,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  List,
  Network
} from 'lucide-vue-next';

import ProductStructureFilters from '@/components/product/ProductStructureFilters.vue';
import ProductStructureTable from '@/components/product/ProductStructureTable.vue';
import ProductStructureDialog from '@/components/product/ProductStructureDialog.vue';
import ProductStructureDetailsDialog from '@/components/product/ProductStructureDetailsDialog.vue';
import ProductStructureVersionDialog from '@/components/product/ProductStructureVersionDialog.vue';
import ProductStructureValidationDialog from '@/components/product/ProductStructureValidationDialog.vue';
import ProductStructureVisualEditor from '@/components/product/ProductStructureVisualEditor.vue';
import ProductStructureDesignDialog from '@/components/product/ProductStructureDesignDialog.vue';

import { useProductStructureStore } from '@/stores/productStructureStore';
import { productStructureService, validationService } from '@/services/productService';
import { testProductStructureDataLoad } from '@/utils/testDataLoad';
import type {
  ProductStructure,
  ProductStructureFilters as Filters,
  StructureValidationResult,
  VersionHistory
} from '@/types/product-structure';

// 使用Store
const productStructureStore = useProductStructureStore();

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');

// 视图模式
const viewMode = ref<'list' | 'visual'>('list');
const selectedStructureForVisual = ref<ProductStructure | null>(null);

// 筛选条件
const filters = reactive<Filters>({
  keyword: '',
  productType: [],
  category: [],
  status: [],
  tags: [],
  applications: []
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 对话框状态
const showCreateDialog = ref(false);
const showDetailsDialog = ref(false);
const showVersionDialog = ref(false);
const showValidationDialog = ref(false);
const editingStructure = ref<ProductStructure | null>(null);
const selectedStructure = ref<ProductStructure | null>(null);
const validationResult = ref<StructureValidationResult | null>(null);

// 设计弹窗状态
const showDesignDialog = ref(false);
const designStructure = ref<ProductStructure | null>(null);

// 计算属性
const filteredStructures = computed(() => {
  return productStructureStore.filteredStructures;
});

const statistics = computed(() => {
  const structures = productStructureStore.structures;
  const total = structures.length;
  const active = structures.filter(s => s.status === 'active').length;
  
  // 计算本月变更数量
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const monthlyChanges = structures.reduce((count, structure) => {
    const lastUpdate = new Date(structure.updatedAt);
    if (lastUpdate.getMonth() === currentMonth && lastUpdate.getFullYear() === currentYear) {
      return count + 1;
    }
    return count;
  }, 0);

  // 计算验证警告数量
  const validationWarnings = structures.reduce((count, structure) => {
    if (structure.lastValidationResult) {
      return count + structure.lastValidationResult.warnings.length;
    }
    return count;
  }, 0);

  return {
    totalStructures: total,
    activeStructures: active,
    monthlyChanges,
    validationWarnings
  };
});

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    await productStructureStore.loadStructures(filters);
    pagination.total = productStructureStore.structures.length;
  } catch (error) {
    console.error('加载产品结构失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = async () => {
  filters.keyword = searchKeyword.value;
  pagination.page = 1;
  await loadData();
};

// 筛选处理
const handleFilter = async () => {
  pagination.page = 1;
  await loadData();
};

// 重置筛选
const handleResetFilters = async () => {
  Object.assign(filters, {
    keyword: '',
    productType: [],
    category: [],
    status: [],
    tags: [],
    applications: []
  });
  searchKeyword.value = '';
  pagination.page = 1;
  await loadData();
};

// 分页处理
const handlePageChange = async (page: number) => {
  pagination.page = page;
  await loadData();
};

const handlePageSizeChange = async (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  await loadData();
};

// 编辑处理
const handleEdit = (structure: ProductStructure) => {
  editingStructure.value = structure;
  showCreateDialog.value = true;
};

// 删除处理
const handleDelete = async (structure: ProductStructure) => {
  if (confirm(`确定要删除产品结构"${structure.name}"吗？`)) {
    try {
      await productStructureService.deleteStructure(structure.id);
      await loadData();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }
};

// 查看详情
const handleViewDetails = (structure: ProductStructure) => {
  selectedStructure.value = structure;
  showDetailsDialog.value = true;
};

// 查看版本历史
const handleViewVersions = (structure: ProductStructure) => {
  selectedStructure.value = structure;
  showVersionDialog.value = true;
};

// 复制结构
const handleDuplicate = async (structure: ProductStructure) => {
  try {
    const duplicatedStructure = {
      ...structure,
      id: undefined,
      code: `${structure.code}_COPY`,
      name: `${structure.name} (副本)`,
      version: 1,
      status: 'draft' as const,
      versionHistory: []
    };
    
    editingStructure.value = duplicatedStructure as ProductStructure;
    showCreateDialog.value = true;
  } catch (error) {
    console.error('复制结构失败:', error);
  }
};

// 验证结构
const handleValidateStructure = async (structure: ProductStructure) => {
  try {
    loading.value = true;
    validationResult.value = await validationService.validateStructure(structure);
    selectedStructure.value = structure;
    showValidationDialog.value = true;
  } catch (error) {
    console.error('验证结构失败:', error);
  } finally {
    loading.value = false;
  }
};

// 批量验证
const validateAllStructures = async () => {
  try {
    loading.value = true;
    // 这里可以实现批量验证逻辑
    console.log('批量验证所有结构');
  } catch (error) {
    console.error('批量验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 恢复版本
const handleRestoreVersion = async (structure: ProductStructure, version: VersionHistory) => {
  try {
    // 实现版本恢复逻辑
    console.log('恢复版本:', version);
    await loadData();
  } catch (error) {
    console.error('恢复版本失败:', error);
  }
};

// 应用修复建议
const handleApplyFix = async (fixAction: any) => {
  try {
    // 实现自动修复逻辑
    console.log('应用修复建议:', fixAction);
    await loadData();
  } catch (error) {
    console.error('应用修复失败:', error);
  }
};

// 保存处理
const handleSave = async (structure: ProductStructure) => {
  try {
    if (editingStructure.value?.id) {
      // 更新
      await productStructureService.updateStructure(structure.id, structure);
    } else {
      // 创建
      await productStructureService.createStructure(structure);
    }
    
    showCreateDialog.value = false;
    editingStructure.value = null;
    await loadData();
  } catch (error) {
    console.error('保存失败:', error);
  }
};

// 取消处理
const handleCancel = () => {
  showCreateDialog.value = false;
  editingStructure.value = null;
};

// 导出结构
const exportStructures = () => {
  // 模拟导出功能
  console.log('导出产品结构');
};

// 设计按钮处理
const handleDesign = (structure: ProductStructure) => {
  designStructure.value = structure;
  showDesignDialog.value = true;
};

// 设计弹窗处理
const handleDesignSave = async (structure: ProductStructure) => {
  try {
    await productStructureService.updateStructure(structure.id, structure);
    await loadData();
    showDesignDialog.value = false;
    // 显示保存成功提示
    console.log('设计保存成功');
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handleDesignValidate = async (structure: ProductStructure) => {
  try {
    const result = await validationService.validateStructure(structure);
    // 显示验证结果
    validationResult.value = result;
    selectedStructure.value = structure;
    showValidationDialog.value = true;
  } catch (error) {
    console.error('验证失败:', error);
  }
};

// 可视化相关处理函数
const selectStructureForVisual = (structure: ProductStructure) => {
  selectedStructureForVisual.value = structure;
};

const handleVisualSave = async (structure: ProductStructure) => {
  try {
    await productStructureService.updateStructure(structure.id, structure);
    await loadData();
    // 显示保存成功提示
    console.log('可视化设计保存成功');
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handleVisualValidate = async (structure: ProductStructure) => {
  try {
    const result = await validationService.validateStructure(structure);
    // 显示验证结果
    validationResult.value = result;
    selectedStructure.value = structure;
    showValidationDialog.value = true;
  } catch (error) {
    console.error('验证失败:', error);
  }
};

const handleVisualUpdate = (structure: ProductStructure) => {
  // 实时更新结构数据，但不保存
  selectedStructureForVisual.value = structure;
};

// 状态辅助函数
const getStatusVariant = (status: string) => {
  const variantMap = {
    active: 'default',
    draft: 'secondary',
    deprecated: 'outline',
    archived: 'destructive'
  };
  return variantMap[status] || 'default';
};

const getStatusText = (status: string) => {
  const textMap = {
    active: '活跃',
    draft: '草稿',
    deprecated: '已弃用',
    archived: '已归档'
  };
  return textMap[status] || status;
};

// 测试数据加载
const testDataLoad = async () => {
  console.log('🔍 开始测试数据加载...');
  const success = await testProductStructureDataLoad();
  if (success) {
    alert('数据加载测试成功！请查看浏览器控制台获取详细信息。');
  } else {
    alert('数据加载测试失败！请查看浏览器控制台获取错误信息。');
  }
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.product-structure-management {
  padding: 1.5rem;
  max-width: 80rem;
  margin: 0 auto;
}
</style>
