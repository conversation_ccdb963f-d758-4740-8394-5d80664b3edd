import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ProductStructureManagement from '../ProductStructureManagement.vue';
import type { ProductStructure } from '@/types/product-structure';

// Mock all the child components
vi.mock('../../../components/product/ProductStructureTable.vue', () => ({
  default: {
    name: 'ProductStructureTable',
    template: '<div class="mock-table"></div>',
    props: ['structures', 'loading', 'pagination'],
    emits: ['edit', 'delete', 'view-details', 'view-versions', 'duplicate', 'validate', 'page-change', 'page-size-change']
  }
}));

vi.mock('../../../components/product/ProductStructureVisualEditor.vue', () => ({
  default: {
    name: 'ProductStructureVisualEditor',
    template: '<div class="mock-visual-editor"></div>',
    props: ['structure', 'readonly'],
    emits: ['save', 'validate', 'update:structure', 'new', 'open']
  }
}));

vi.mock('../../../components/product/ProductStructureFilters.vue', () => ({
  default: {
    name: 'ProductStructureFilters',
    template: '<div class="mock-filters"></div>',
    props: ['filters'],
    emits: ['update:filters', 'reset']
  }
}));

// Mock services
vi.mock('@/services/productService', () => ({
  productStructureService: {
    getStructures: vi.fn().mockResolvedValue({
      data: [
        {
          id: 'struct1',
          code: 'WIN001',
          name: '标准窗户结构',
          description: '标准铝合金窗户产品结构',
          version: 1,
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          createdBy: 'user1',
          updatedBy: 'user1'
        }
      ],
      total: 1,
      page: 1,
      pageSize: 20
    }),
    updateStructure: vi.fn().mockResolvedValue({}),
    deleteStructure: vi.fn().mockResolvedValue({})
  }
}));

vi.mock('@/services/validationService', () => ({
  validationService: {
    validateStructure: vi.fn().mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: []
    })
  }
}));

describe('ProductStructureManagement Integration', () => {
  let mockStructures: ProductStructure[];

  beforeEach(() => {
    mockStructures = [
      {
        id: 'struct1',
        code: 'WIN001',
        name: '标准窗户结构',
        description: '标准铝合金窗户产品结构',
        version: 1,
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        createdBy: 'user1',
        updatedBy: 'user1',
        rootAssembly: {
          id: 'asm1',
          assemblyId: 'asm1',
          assemblyCode: 'WIN_FRAME',
          assemblyName: '窗框构件',
          assemblyVersion: 1,
          instanceName: '主窗框',
          quantity: 1,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          parameterValues: {},
          optional: false,
          alternatives: [],
          properties: {}
        },
        parameters: [],
        constraints: [],
        validationRules: [],
        metadata: {},
        tags: []
      }
    ];
  });

  it('应该正确渲染产品结构管理页面', async () => {
    const wrapper = mount(ProductStructureManagement);

    // 等待数据加载
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.product-structure-management').exists()).toBe(true);
    expect(wrapper.find('h1').text()).toContain('产品结构管理');
  });

  it('应该支持视图模式切换', async () => {
    const wrapper = mount(ProductStructureManagement);

    // 初始应该是列表视图
    expect(wrapper.vm.viewMode).toBe('list');
    expect(wrapper.find('.mock-table').exists()).toBe(true);

    // 切换到可视化视图
    wrapper.vm.viewMode = 'visual';
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.mock-visual-editor').exists()).toBe(true);
  });

  it('应该正确处理结构选择用于可视化', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 切换到可视化视图
    wrapper.vm.viewMode = 'visual';
    await wrapper.vm.$nextTick();

    // 选择一个结构进行可视化
    wrapper.vm.selectStructureForVisual(mockStructures[0]);
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.selectedStructureForVisual).toEqual(mockStructures[0]);
  });

  it('应该正确处理可视化编辑器的保存事件', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 设置可视化模式和选中结构
    wrapper.vm.viewMode = 'visual';
    wrapper.vm.selectedStructureForVisual = mockStructures[0];
    await wrapper.vm.$nextTick();

    const visualEditor = wrapper.findComponent({ name: 'ProductStructureVisualEditor' });
    expect(visualEditor.exists()).toBe(true);

    // 模拟保存事件
    const updatedStructure = { ...mockStructures[0], version: 2 };
    await visualEditor.vm.$emit('save', updatedStructure);

    // 验证保存处理逻辑被调用
    // 这里可以检查服务调用或状态更新
  });

  it('应该正确处理可视化编辑器的验证事件', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 设置可视化模式和选中结构
    wrapper.vm.viewMode = 'visual';
    wrapper.vm.selectedStructureForVisual = mockStructures[0];
    await wrapper.vm.$nextTick();

    const visualEditor = wrapper.findComponent({ name: 'ProductStructureVisualEditor' });
    
    // 模拟验证事件
    await visualEditor.vm.$emit('validate', mockStructures[0]);

    // 验证验证处理逻辑被调用
    expect(wrapper.vm.showValidationDialog).toBe(true);
  });

  it('应该在列表视图和可视化视图之间保持数据同步', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 在列表视图中加载数据
    await wrapper.vm.loadData();
    const originalStructures = wrapper.vm.structures;

    // 切换到可视化视图
    wrapper.vm.viewMode = 'visual';
    wrapper.vm.selectStructureForVisual(originalStructures[0]);
    await wrapper.vm.$nextTick();

    // 在可视化编辑器中更新结构
    const updatedStructure = { ...originalStructures[0], name: '更新的窗户结构' };
    wrapper.vm.handleVisualUpdate(updatedStructure);

    // 验证选中的结构已更新
    expect(wrapper.vm.selectedStructureForVisual.name).toBe('更新的窗户结构');
  });

  it('应该正确显示状态徽章', () => {
    const wrapper = mount(ProductStructureManagement);

    // 测试不同状态的徽章变体
    expect(wrapper.vm.getStatusVariant('active')).toBe('default');
    expect(wrapper.vm.getStatusVariant('draft')).toBe('secondary');
    expect(wrapper.vm.getStatusVariant('deprecated')).toBe('outline');
    expect(wrapper.vm.getStatusVariant('archived')).toBe('destructive');

    // 测试状态文本
    expect(wrapper.vm.getStatusText('active')).toBe('活跃');
    expect(wrapper.vm.getStatusText('draft')).toBe('草稿');
    expect(wrapper.vm.getStatusText('deprecated')).toBe('已弃用');
    expect(wrapper.vm.getStatusText('archived')).toBe('已归档');
  });

  it('应该在可视化视图中显示结构选择界面', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 切换到可视化视图但不选择结构
    wrapper.vm.viewMode = 'visual';
    wrapper.vm.selectedStructureForVisual = null;
    await wrapper.vm.$nextTick();

    // 应该显示结构选择界面
    const selectionInterface = wrapper.find('.text-center');
    expect(selectionInterface.exists()).toBe(true);
    expect(selectionInterface.text()).toContain('选择产品结构进行可视化设计');
  });

  it('应该支持从可视化视图快速选择结构', async () => {
    const wrapper = mount(ProductStructureManagement);
    
    // 设置测试数据
    wrapper.vm.structures = mockStructures;
    wrapper.vm.viewMode = 'visual';
    await wrapper.vm.$nextTick();

    // 模拟点击结构选择
    const structureItem = wrapper.find('.cursor-pointer');
    if (structureItem.exists()) {
      await structureItem.trigger('click');
      expect(wrapper.vm.selectedStructureForVisual).toBeTruthy();
    }
  });
});
